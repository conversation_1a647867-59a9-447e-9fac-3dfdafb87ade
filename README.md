# Loki Migration Tool
## Overview
The Loki Migration Tool is a comprehensive utility designed to migrate various data types from DeepThought UI to Loki.
It supports multiple migration types including FBL rules and bid unit configurations, with a modular architecture
that makes it easy to add new migration types.

## Features
- **Multiple Migration Types**: Rules, Bid Unit Configurations, and extensible for more
- **Modular Architecture**: Each migrator is independent and can be run separately
- **Automated migration** to Loki with intelligent duplicate detection
- **Dry run capability** to preview changes before execution
- **CSV output** for rule transformations and validation
- **Detailed logging** with both main and target-specific logs
- **Multi-partner support**: Google Hotel Ads, TripAdvisor, and Trivago

## Prerequisites
- Python 3.7 or higher
- Required Python packages (see Installation section)

## Installation
1. Clone the repository:
``` bash
   git clone https://github.expedia.biz/cargabright/loki-migration-tool.git
   cd loki-migration-tool
```
1. Install dependencies:
``` bash
   pip install -r requirements.txt
```
## Architecture
The tool is organized into a modular architecture:

```
/migrators/
├── __init__.py
├── base_migrator.py          # Abstract base class with common functionality
├── rule_migrator.py          # Rule migration implementation
└── bid_unit_config_migrator.py  # Bid unit config migration (template)

main.py                       # Orchestrator that runs different migrators
```

### Available Migrators
1. **RuleMigrator**: Migrates FBL rules from DeepThought to Loki
2. **BidUnitConfigMigrator**: Template for bid unit configuration migration (to be implemented)

## Usage
### Configuration
Modify the `config.yaml` file with your desired settings:
- **Environment settings**: Choose between 'prod' and 'test' for both Loki and DeepThought
- **Server addresses**: Automatically selected based on environment
- **Schema ID**: Set to the RuleSchemaId that RuleSets should operate on (7 for prod, 24 for test)
- **Dry run**: Set to `true` or `false` to preview changes before execution

### Running Migrations
The main.py file provides several options for running migrations:

#### Option 1: Run Only Rule Migration
```python
# In main.py, uncomment this line:
run_rule_migration(targets)
```

#### Option 2: Run Only Bid Unit Config Migration
```python
# In main.py, uncomment this line:
run_bid_unit_config_migration(targets)
```

#### Option 3: Run All Migrations
```python
# In main.py, uncomment this line:
run_all_migrations(targets)
```

Then run:
```bash
python3 main.py
```

## Dry Run Mode
The dry run feature allows you to preview the migration process without making any actual changes to the target system.
This is useful for validating your configuration and understanding the expected outcome.
When you run the tool with the `dry_run` flag:

The tool will:
1. Create a directory named `dry_run_output` in the current working directory
2. Generate CSV files within this directory that represent each rule and rule set that would be migrated
3. The CSV files contain details about:
    - Transformed rule for Loki and should match your schema as designed in Loki

## Example Output Structure
After a dry run, you'll find a directory structure like:
``` 
dry_run_output/
├── GOOGLE_WOTIF_HOTELS_WTF:H:AU:$:CPC:DESKTOP:X_WTF:AU:$:CPC:DESKTOP_Magneto_3.3.1_BAU.csv
├── GOOGLE_WOTIF_HOTELS_WTF:H:AU:$:CPC:MOBILE:X_WTF:AU:$:CPC:MOBILE:X:ALL:ALL:ALL:PROMOTED_Magneto_3.3.1_BAU.csv
└── GOOGLE_WOTIF_HOTELS_WTF:H:AU:$:CPC:MOBILE:X_WTF:AU:$:CPC:MOBILETABLET_Magneto_3.3.1_BAU.csv
```
Each CSV file provides detailed information about the rules that would be migrated, allowing you to review and adjust 
your configuration as needed before performing the actual migration.

## Troubleshooting
If you encounter issues:
1. Check your configuration file for errors
2. Ensure your source and target systems are accessible
3. Verify your credentials have the necessary permissions

## Extending the Tool
### Adding New Migrators
To add a new migrator type:

1. **Create a new migrator class** in the `migrators/` directory:
```python
# migrators/my_new_migrator.py
from .base_migrator import BaseMigrator

class MyNewMigrator(BaseMigrator):
    def __init__(self):
        super().__init__("MyNewMigrator")
        # Initialize any specific services

    def process_target(self, target_string: str) -> None:
        # Implement your migration logic here
        pass
```

2. **Update the migrators/__init__.py** to export your new migrator:
```python
from .my_new_migrator import MyNewMigrator
__all__ = ['RuleMigrator', 'BidUnitConfigMigrator', 'MyNewMigrator']
```

3. **Add a function in main.py** to run your migrator:
```python
def run_my_new_migration(targets: List[str]) -> None:
    logger.info("Starting My New Migration")
    migrator = MyNewMigrator()
    migrator.run(targets)
    logger.info("My New Migration completed")
```

### Benefits of the Base Migrator
The `BaseMigrator` class provides:
- Common service initialization (Loki, DeepThought)
- Standardized logging setup
- Counter management (created, updated, failed, etc.)
- Consistent error handling and timing
- Dry run support

## Contributing
Contributions are welcome! Please feel free to submit a Pull Request.

