# Loki Rule Migration Tool
## Overview
The Loki Rule Migration Tool is a utility designed to migrate FBLs from Deepthought UI to Loki.
It helps automate the process of transforming and transferring rule configurations between basebids, multipliers, 
and global FBLs.
## Features
- Automated rule migration to Loki
- Dry run capability to preview changes before execution
- CSV output for rule transformations
- Detailed logging for debugging

## Prerequisites
- Python 3.7 or higher
- Required Python packages (see Installation section)

## Installation
1. Clone the repository:
``` bash
   git clone https://github.expedia.biz/cargabright/loki-migration-tool.git
   cd loki-migration-tool
```
1. Install dependencies:
``` bash
   pip install -r requirements.txt
```
## Usage
### Basic Usage
Modify the `config/settings.py` file with your desired settings
Important settings are
- loki_server_address: set this to use prod or test env for gRPC calls 
- dtui_api_url: set this to use prod or test env to fetch FBLs from
- schema_id: Set this to the RuleSchemaId that RuleSets should operate on
- dry_run: Set to `True` or `False` to generate sample files for validation before making gRPC calls to <PERSON>

Run the migration tool with your configurations set in settings.py:
``` bash
python main.py
```

## Dry Run Mode
The dry run feature allows you to preview the migration process without making any actual changes to the target system.
This is useful for validating your configuration and understanding the expected outcome.
When you run the tool with the `dry_run` flag:

The tool will:
1. Create a directory named `dry_run_output` in the current working directory
2. Generate CSV files within this directory that represent each rule and rule set that would be migrated
3. The CSV files contain details about:
    - Transformed rule for Loki and should match your schema as designed in Loki

## Example Output Structure
After a dry run, you'll find a directory structure like:
``` 
dry_run_output/
├── GOOGLE_WOTIF_HOTELS_WTF:H:AU:$:CPC:DESKTOP:X_WTF:AU:$:CPC:DESKTOP_Magneto_3.3.1_BAU.csv
├── GOOGLE_WOTIF_HOTELS_WTF:H:AU:$:CPC:MOBILE:X_WTF:AU:$:CPC:MOBILE:X:ALL:ALL:ALL:PROMOTED_Magneto_3.3.1_BAU.csv
└── GOOGLE_WOTIF_HOTELS_WTF:H:AU:$:CPC:MOBILE:X_WTF:AU:$:CPC:MOBILETABLET_Magneto_3.3.1_BAU.csv
```
Each CSV file provides detailed information about the rules that would be migrated, allowing you to review and adjust 
your configuration as needed before performing the actual migration.

## Troubleshooting
If you encounter issues:
1. Check your configuration file for errors
2. Ensure your source and target systems are accessible
3. Verify your credentials have the necessary permissions

## Contributing
Contributions are welcome! Please feel free to submit a Pull Request.

