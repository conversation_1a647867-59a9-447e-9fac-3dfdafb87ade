loki_environment: "test"  # Using TEST Loki environment
deepthought_environment: "prod"  # Using PROD DeepThought environment

loki:
  servers:
    prod: "loki-server.marketing.expedia.com:443"
    test: "loki-server.test.marketing.expedia.com:443"
  use_ssl: true

deepthought:
  servers:
    prod: "https://deepthought-backend-service.rcp.us-east-1.marketing.prod.exp-aws.net"
    test: "https://deepthought-backend-service.rcp.us-east-1.marketing.test.exp-aws.net"

grpc:
  timeout_seconds: 30

rules:
  attachment_type: 2
  marketing_channels:
    GOOGLE: "MARKETING_CHANNEL_GOOGLE_HOTEL_ADS"
    TRIPADVISOR: "MARKETING_CHANNEL_TRIP_ADVISOR"
    KAYAK: "MARKETING_CHANNEL_KAYAK"
    TRIVAGO: "MARKETING_CHANNEL_TRIVAGO"
  marketing_channel: "TRIVAGO"
  schema_id: "24" # 24 for Test ENV and 7 for Prod ENV
  exclude_factors_of_one: false

execution:
  dry_run: false
  dry_run_output_folder: "dry_run_output"
