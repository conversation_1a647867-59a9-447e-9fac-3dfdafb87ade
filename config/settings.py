from pathlib import Path
from typing import Optional, Dict, Any

import yaml


class Settings:

    def __init__(self, config_file: Optional[str] = None):
        """
        Initialize settings from the config file and environment variables.

        Args:
            config_file: Path to config file. Defaults to 'config.yaml' in the project root.
        """
        self.config_file = config_file or self._find_config_file()
        self.config = self._load_config()

        # Environment selection
        self.deepthought_environment: str = self.config.get('deepthought_environment', 'test')
        self.loki_environment: str = self.config.get('loki_environment', 'test')

        # Loki settings
        # Loki settings
        loki_config = self.config.get('loki', {})
        loki_servers = loki_config.get('servers', {})

        # Loki gRPC server settings
        self.loki_prod_server_address: str = loki_servers.get('prod', 'loki-server.marketing.expedia.com:443')
        self.loki_test_server_address = loki_servers.get('test', 'loki-server.test.marketing.expedia.com:443')
        self.loki_server_address = loki_servers.get(self.loki_environment, self.loki_test_server_address)
        self.loki_use_ssl = loki_config.get('use_ssl', True)

        # DeepThought settings
        deepthought_config = self.config.get('deepthought', {})
        deepthought_servers = deepthought_config.get('servers', {})

        self.dtui_prod_api_url = deepthought_servers.get('prod',
                                                'https://deepthought-backend-service.rcp.us-east-1.marketing.prod.exp-aws.net')
        self.dtui_test_api_url = deepthought_servers.get('test',
                                                'https://deepthought-backend-service.rcp.us-east-1.marketing.test.exp-aws.net')
        self.dtui_api_url = deepthought_servers.get(self.deepthought_environment, self.dtui_prod_api_url)

        # DeepThought API settings
        self.dtui_prod_api_url: str = 'https://deepthought-backend-service.rcp.us-east-1.marketing.prod.exp-aws.net'
        self.dtui_test_api_url: str = 'https://deepthought-backend-service.rcp.us-east-1.marketing.test.exp-aws.net'
        self.dtui_api_url: str = self.dtui_prod_api_url

        # gRPC settings
        grpc_config = self.config.get('grpc', {})
        self.grpc_timeout_seconds = grpc_config.get('timeout_seconds', 30)

        # Rule settings
        rules_config = self.config.get('rules', {})
        self.attachment_type = rules_config.get('attachment_type')
        self.marketing_channels = rules_config.get('marketing_channels')
        self.marketing_channel = self.marketing_channels.get(rules_config.get('marketing_channel', 'MARKETING_CHANNEL_GOOGLE_HOTEL_ADS'))
        self.exclude_factors_of_one = rules_config.get('exclude_factors_of_one', True)

        # Schema IDs
        self.schema_id = str(rules_config.get('schema_id'))

        # Execution settings
        execution_config = self.config.get('execution', {})
        self.dry_run = execution_config.get('dry_run', False)
        self.dry_run_output_folder = execution_config.get('dry_run_output_folder', 'dry_run_output')

    @staticmethod
    def _find_config_file() -> str:
        """Find the config file in common locations"""
        possible_paths = [
            'config.yaml',
            'config/config.yaml',
            Path(__file__).parent.parent / 'config.yaml'
        ]

        for path in possible_paths:
            if Path(path).exists():
                return str(path)

        # Return a default path if none found
        return 'config.yaml'

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        try:
            with open(self.config_file, 'r') as file:
                return yaml.safe_load(file) or {}
        except FileNotFoundError:
            print(f"Warning: Config file '{self.config_file}' not found. Using defaults.")
            return {}
        except yaml.YAMLError as e:
            print(f"Error parsing config file '{self.config_file}': {e}")
            return {}
