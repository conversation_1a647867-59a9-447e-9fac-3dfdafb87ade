"""
Debug script to test the exact API call being made to DeepThought
"""
import requests
import urllib3
from models.target import TVGBidTarget

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_api_call():
    """Test the exact API call being made"""
    
    target_string = 'TRIVAGO|EXPEDIA|HOTELS|US|SPA|Magneto|3.7.3|PROD'
    target = TVGBidTarget.from_pipe_string(target_string)
    
    print(f"Target parsed as: {target}")
    print(f"Partner: '{target.partner}'")
    print(f"Brand: '{target.brand}'")
    print(f"LOB: '{target.lob}'")
    print(f"POSA: '{target.posa}'")
    print(f"Auction Type: '{target.auction}'")
    print(f"Model Name: '{target.model_name}'")
    print(f"Model Version: '{target.model_version}'")
    
    # Test the exact API call
    base_url = "https://deepthought-backend-service.rcp.us-east-1.marketing.prod.exp-aws.net"
    endpoint = "/campaignStore/allFbl"
    
    params = {
        "channel": "meta",
        "lob": target.lob,
        "partner": target.partner,
        "brand": target.brand,
        "algoName": target.model_name,
        "algoVersion": target.model_version,
        "posa": target.posa,
        "auction": target.auction,
    }
    
    print(f"\nAPI Call Details:")
    print(f"URL: {base_url}{endpoint}")
    print(f"Parameters: {params}")
    
    # Try different case variations
    test_cases = [
        ("Original", params),
        ("Uppercase Brand", {**params, "brand": target.brand.upper()}),
        ("Lowercase Brand", {**params, "brand": target.brand.lower()}),
        ("Uppercase Partner", {**params, "partner": target.partner.upper()}),
        ("Lowercase Partner", {**params, "partner": target.partner.lower()}),
        ("Uppercase POSA", {**params, "posa": target.posa.upper()}),
        ("Lowercase POSA", {**params, "posa": target.posa.lower()}),
    ]
    
    for test_name, test_params in test_cases:
        print(f"\n--- Testing {test_name} ---")
        print(f"Params: {test_params}")
        
        try:
            response = requests.get(f"{base_url}{endpoint}", params=test_params, verify=False)
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ SUCCESS! Found data with keys: {list(data.keys())}")
                basebid_ops = data.get('basebidOperations', {})
                if isinstance(basebid_ops, dict):
                    basebid_list = basebid_ops.get('basebidOperationList', [])
                    print(f"   Basebid operations: {len(basebid_list)}")
                break
            else:
                try:
                    error_data = response.json()
                    print(f"❌ Error: {error_data}")
                except:
                    print(f"❌ Error: {response.text[:200]}")
                    
        except Exception as e:
            print(f"❌ Exception: {e}")

if __name__ == "__main__":
    test_api_call()
