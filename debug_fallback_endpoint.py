"""
Debug script to examine the data structure returned by the fallback endpoint
"""
import requests
import urllib3
import json
from models.target import TVGBidTarget

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def debug_fallback_endpoint():
    """Debug the fallback endpoint response structure"""
    
    target_string = 'TRIVAGO|EXPEDIA|HOTELS|US|SPA|Magneto|3.7.3|PROD'
    target = TVGBidTarget.from_pipe_string(target_string)
    
    print(f"Testing fallback endpoint for: {target_string}")
    
    # Test the fallback endpoint
    base_url = "https://deepthought-backend-service.rcp.us-east-1.marketing.prod.exp-aws.net"
    fallback_endpoint = f"/campaignStore/allAssociatedFbl/{target.model_name}/{target.model_version}"
    
    params = {
        "channel": "meta",
        "lob": target.lob,
        "partner": target.partner,
        "brand": target.brand,
        "algoName": target.model_name,
        "algoVersion": target.model_version,
        "posa": target.posa,
        "auction": target.auction_type,
    }
    
    print(f"\nFallback API Call:")
    print(f"URL: {base_url}{fallback_endpoint}")
    print(f"Parameters: {params}")
    
    try:
        response = requests.get(f"{base_url}{fallback_endpoint}", params=params, verify=False)
        print(f"\nStatus Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ SUCCESS! Response structure:")
            print(f"Top-level keys: {list(data.keys())}")
            
            # Pretty print the structure
            print(f"\nFull response structure:")
            print(json.dumps(data, indent=2)[:2000] + "..." if len(str(data)) > 2000 else json.dumps(data, indent=2))
            
            # Check for basebidOperations
            basebid_ops = data.get('basebidOperations')
            print(f"\nbasebidOperations: {type(basebid_ops)}")
            if basebid_ops:
                print(f"basebidOperations keys: {list(basebid_ops.keys()) if isinstance(basebid_ops, dict) else 'Not a dict'}")
            
            # Check for multiplierOperations
            multiplier_ops = data.get('multiplierOperations')
            print(f"multiplierOperations: {type(multiplier_ops)}")
            if multiplier_ops:
                print(f"multiplierOperations keys: {list(multiplier_ops.keys()) if isinstance(multiplier_ops, dict) else 'Not a dict'}")
                
        else:
            try:
                error_data = response.json()
                print(f"❌ Error: {error_data}")
            except:
                print(f"❌ Error: {response.text[:500]}")
                
    except Exception as e:
        print(f"❌ Exception: {e}")

if __name__ == "__main__":
    debug_fallback_endpoint()
