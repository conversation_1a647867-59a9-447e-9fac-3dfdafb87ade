"""
Debug script to investigate why migration has 0 items processed
"""
import logging
from config.settings import Settings
from services.deepthought_service import DeepThoughtService
from models.target import TVGBidTarget

# Set up logging
logging.basicConfig(level=logging.DEBUG,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("DebugMigration")

def debug_target_processing():
    """Debug the target processing to see where it fails"""
    
    # Use the same target that failed
    target_string = 'TRIVAGO|EXPEDIA|HOTELS|UK|SPA|Magneto|3.7.3|PROD'
    
    logger.info(f"Debugging target: {target_string}")
    
    # Initialize settings and services
    settings = Settings()
    logger.info(f"DeepThought environment: {settings.deepthought_environment}")
    logger.info(f"DeepThought API URL: {settings.dtui_api_url}")
    
    dt_service = DeepThoughtService(base_url=settings.dtui_api_url)
    
    # Parse target
    target = TVGBidTarget.from_pipe_string(target_string)
    logger.info(f"Parsed target: {target}")
    
    try:
        # Try to fetch FBLs
        logger.info("Attempting to fetch FBLs...")
        fbls = dt_service.fetch_tvg_fbls(
            lob=target.lob,
            partner=target.partner,
            brand=target.brand,
            algoName=target.model_name,
            algoVersion=target.model_version,
            posa=target.posa,
            auction=target.auction_type
        )
        logger.info(f"Successfully fetched FBLs: {type(fbls)}")
        logger.info(f"FBL keys: {list(fbls.keys()) if isinstance(fbls, dict) else 'Not a dict'}")
        
        # Check if we have basebid operations
        basebid_ops = fbls.get('basebidOperations', {})
        logger.info(f"Basebid operations: {type(basebid_ops)}")
        if isinstance(basebid_ops, dict):
            basebid_list = basebid_ops.get('basebidOperationList', [])
            logger.info(f"Basebid operation list length: {len(basebid_list)}")
        
        # Check if we have multiplier operations
        multiplier_ops = fbls.get('multiplierOperations', {})
        logger.info(f"Multiplier operations: {type(multiplier_ops)}")
        if isinstance(multiplier_ops, dict):
            multiplier_list = multiplier_ops.get('multiplierOperationList', [])
            logger.info(f"Multiplier operation list length: {len(multiplier_list)}")
            
    except Exception as e:
        logger.error(f"Error fetching FBLs: {e}", exc_info=True)
        return
    
    try:
        # Try to fetch global FBLs
        logger.info("Attempting to fetch global FBLs...")
        global_fbls = dt_service.fetch_global_fbls(
            brand_name=target.brand,
            channel="META",
            lob=target.lob,
            partner=target.partner
        )
        logger.info(f"Successfully fetched global FBLs: {type(global_fbls)}")
        logger.info(f"Global FBL keys: {list(global_fbls.keys()) if isinstance(global_fbls, dict) else 'Not a dict'}")
        
    except Exception as e:
        logger.error(f"Error fetching global FBLs: {e}", exc_info=True)
        return

if __name__ == "__main__":
    debug_target_processing()
