"""
Rule Migrator - Transfers rules from DeepThought to Loki
"""
import logging
import os
import time
from typing import List, Tuple

from config.settings import Settings
from models.rule import RuleItem
from services.loki_service import LokiService
from services.deepthought_service import DeepThoughtService
from services.rule_generator import RuleGenerator
from models.target import TABidTarget, GHABidTarget, TVGBidTarget
from utils.builders import build_rule_item_payload

# Set up logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("RuleMigrator")
from utils.logging_utils import setup_target_logger


class RuleMigrator:
    """Main application class that orchestrates the rule migration process"""

    def __init__(self):
        self.settings = Settings()
        self.main_logger = logger  # Use the existing module-level logger

        self.loki_service = LokiService(
            self.settings,
            server_address=self.settings.loki_server_address,
            use_ssl=self.settings.loki_use_ssl,
            timeout_seconds=60
        )
        self.dt_service = DeepThoughtService(
            base_url=self.settings.dtui_api_url
        )
        self.rule_generator = RuleGenerator(loki_service=self.loki_service, deepthought_service=self.dt_service, settings=self.settings)
        self.item_count = 0
        self.updated_count = 0
        self.created_count = 0
        self.no_change_count = 0
        self.failed_count = 0

        # Create logs directory if it doesn't exist
        os.makedirs("logs", exist_ok=True)

        # The main logger is already configured at the module level
        self.main_logger = logger

    def process_target(self, target_string: str) -> None:
        """Process a single target string"""
        # Create a safe filename for the log file
        target_logger, file_handler = setup_target_logger(target_string)

        # Set the logger for the loki service for this target
        self.loki_service.set_logger(target_logger)
        self.dt_service.set_logger(target_logger)

        # Log to both the main logger and the target-specific logger
        self.main_logger.info(f"Processing target: {target_string}")
        target_logger.info(f"Starting processing for target: {target_string}")

        try:
            # Parse target
            if "TRIPADVISOR" in target_string:
                target = TABidTarget.from_pipe_string(target_string)
                fbls = self.dt_service.fetch_ta_fbls(
                    lob=target.lob,
                    partner=target.partner,
                    brand=target.brand,
                    algoName=target.model_name,
                    algoVersion=target.model_version,
                    posa=target.posa,
                    device=target.device
                )
            elif "TRIVAGO" in target_string:
                target = TVGBidTarget.from_pipe_string(target_string)
                fbls = self.dt_service.fetch_tvg_fbls(
                    lob=target.lob,
                    partner=target.partner,
                    brand=target.brand,
                    algoName=target.model_name,
                    algoVersion=target.model_version,
                    posa=target.posa,
                    auction=target.auction_type
                )
            elif "GOOGLE" in target_string:
                target = GHABidTarget.from_pipe_string(target_string)
                fbls = self.dt_service.fetch_gha_fbls(
                    lob=target.lob,
                    partner=target.partner,
                    brand=target.brand,
                    algoName=target.model_name,
                    algoVersion=target.model_version,
                    account=target.account,
                    campaign=target.campaign
                )
            else:
                error_msg = f"Unsupported target type: {target_string}"
                self.main_logger.error(error_msg)
                target_logger.error(error_msg)
                file_handler.close()
                target_logger.removeHandler(file_handler)
                return

            # Fetch global FBLs
            target_logger.info(f"Fetching global FBLs for {target.brand}, {target.lob}, {target.partner}")
            global_fbls = self.dt_service.fetch_global_fbls(
                brand_name=target.brand,
                channel="META",
                lob=target.lob,
                partner=target.partner
            )

            # Generate rule items
            target_logger.info(f"Generating rule items")
            df_rule_set = self.rule_generator.generate_rule_items(
                target=target,
                fbls=fbls,
                global_fbls=global_fbls,
                exclude_factors_of_one=self.settings.exclude_factors_of_one
            )
            target_logger.info(f"Generated {len(df_rule_set)} rule items")

            # Create the rule in Loki
            try:
                labels = {
                    "lob": target.lob,
                    "partner": target.partner,
                    "brand": target.brand,
                }

                grouped_rule_sets = df_rule_set.groupby('rule_name')
                for rule_name, df_rule_items in grouped_rule_sets:
                    rule_id = None
                    rule_name = f"{rule_name}".strip()
                    rule_payload = {
                        'rule_attachment_type': self.settings.attachment_type,
                        'labels': labels,
                        'marketing_channel': self.settings.marketing_channels.get(target.partner),
                        'name': rule_name,
                        'schema_id': self.settings.schema_id
                    }

                    if not self.settings.dry_run:
                        try:
                            target_logger.info(f"Getting or creating rule: {rule_name}")
                            rule_id = self.loki_service.get_or_create_rule(**rule_payload)
                            target_logger.info(f"Rule ID: {rule_id}")
                        except Exception as e:
                            error_msg = f"Error creating rule {rule_name}: {e}"
                            self.main_logger.error(error_msg, exc_info=True)
                            target_logger.error(error_msg, exc_info=True)
                            raise e
                    else:
                        rule_id = "dry_run"
                        target_logger.info(f"Dry run: would create/get rule {rule_name}")

                    rule_items = [RuleItem(**item) for item in df_rule_items.to_dict(orient='records')]
                    target_logger.info(f"Processing {len(rule_items)} rule items")

                    for item in rule_items:
                        self.item_count += 1

                        item_payload = build_rule_item_payload(
                            item=item,
                            business_case=target.business_use_case,
                            rule_id=rule_id
                        )
                        if not self.settings.dry_run:
                            result_id, action = self.loki_service.create_or_update_rule_item(**item_payload)

                            if action == 'created':
                                self.created_count += 1
                                msg = f"Created rule item {item.priority}: {item.resource} {item.operator} (ID: {result_id})"
                                self.main_logger.info(msg)
                                target_logger.info(msg)
                            elif action == 'updated':
                                self.updated_count += 1
                                msg = f"Updated rule item {item.priority}: {item.resource} {item.operator} (ID: {result_id})"
                                self.main_logger.info(msg)
                                target_logger.info(msg)
                            elif action == 'no_change':
                                self.no_change_count += 1
                                msg = f"No changes for rule item {item.priority}: {item.resource} {item.operator} (ID: {result_id})"
                                self.main_logger.info(msg)
                                target_logger.info(msg)
                            elif action == 'failed':
                                self.failed_count += 1
                                msg = f"Failed to create or update rule item {item.priority}: {item.resource} {item.operator}"
                                self.main_logger.error(msg)
                                target_logger.error(msg)
                            else:
                                msg = f"Unknown action {action} for rule item {item.priority}: {item.resource} {item.operator}"
                                self.main_logger.error(msg)
                                target_logger.error(msg)
                        else:
                            msg = f"Would have created/updated rule item {item.priority}: {item.resource} {item.operator} {item.dimensions}"
                            self.main_logger.info(msg)
                            target_logger.info(msg)

            except Exception as e:
                error_msg = f"Error processing rule {target_string}: {e}"
                self.main_logger.error(error_msg, exc_info=True)
                target_logger.error(error_msg, exc_info=True)

            if self.settings.dry_run:
                filename = f"./{self.settings.dry_run_output_folder}/{target_string.replace('|', '_')}.csv"
                # Create the directory if it doesn't exist
                os.makedirs(self.settings.dry_run_output_folder, exist_ok=True)
                df_rule_set.to_csv(filename, index=False)
                target_logger.info(f"Wrote dry run output to {filename}")
            else:
                # Log summary only displayed when not running in dry run mode
                target_logger.info(f"Target processing complete. Items: {self.item_count}, Created: {self.created_count}, "
                                   f"Updated: {self.updated_count}, No changes: {self.no_change_count}, "
                                   f"Failed: {self.failed_count}")
        finally:
            # Close and remove the file handler
            target_logger.info(f"Finished processing target: {target_string}")
            file_handler.close()
            target_logger.removeHandler(file_handler)

            # Reset the loki service logger to the default
            self.loki_service.reset_logger()
            self.dt_service.reset_logger()

    def run(self, targets: List[str]) -> None:
        """Run the migration for all targets"""
        start_time = time.time()
        self.main_logger.info(f"Starting migration process for {len(targets)} targets")

        try:
            with self.loki_service.create_channel() as channel:
                for target_string in targets:
                    self.process_target(target_string)
            if self.settings.dry_run:
                self.main_logger.info(f"Dry run completed successfully, results can be viewed at: ./dry_run_output")
        except Exception as e:
            self.main_logger.error(f"Error during migration: {e}", exc_info=True)

        elapsed_time = time.time() - start_time
        self.main_logger.info(f"Rule migration completed in {elapsed_time:.2f} seconds.")
        self.main_logger.info(f"Total rule items processed: {self.item_count}")
        if not self.settings.dry_run:
            self.main_logger.info(f"Created: {self.created_count}, Updated: {self.updated_count}, "
                                  f"No changes: {self.no_change_count}, Failed: {self.failed_count}")


def main():
    # targets = [
    #     'GOOGLE|WOTIF|HOTELS|WTF:H:AU:$:CPC:MOBILE:X|WTF:AU:$:CPC:MOBILE:X:ALL:ALL:ALL:PROMOTED|Magneto|3.3.1|BAU',
    #     'GOOGLE|WOTIF|HOTELS|WTF:H:AU:$:CPC:DESKTOP:X|WTF:AU:$:CPC:DESKTOP:X:ALL:ALL:ALL:PROMOTED|Magneto|3.3.1|BAU',
    #     'GOOGLE|WOTIF|HOTELS|WTF:H:NZ:$:CPC:DESKTOP:X|WTF:NZ:$:CPC:DESKTOP:X:ALL:ALL:ALL:PROMOTED|Magneto|3.3.1|BAU',
    #     'GOOGLE|WOTIF|HOTELS|WTF:H:AU:$:CPC:MOBILE:X|WTF:AU:$:CPC:MOBILETABLET|Magneto|3.3.1|BAU',
    #     'GOOGLE|WOTIF|HOTELS|WTF:H:AU:$:CPC:DESKTOP:X|WTF:AU:$:CPC:DESKTOP|Magneto|3.3.1|BAU',
    #     'GOOGLE|WOTIF|HOTELS|WTF:H:NZ:$:CPC:MOBILE:X|WTF:NZ:$:CPC:MOBILE:X:ALL:ALL:ALL:PROMOTED|Magneto|3.3.1|BAU',
    # ]

    # targets = [
    #     'TRIPADVISOR|HCOM|HOTELS|AU|DESKTOP|Magneto|3.3.1|BAU'
    # ]

    targets = [
        # 'TRIVAGO|WOTIF|HOTELS|NZ|SPA|Magneto|3.7.3|PROD', # DONE
        # 'TRIVAGO|WOTIF|HOTELS|AU|SPA|Magneto|3.6.1|PROD', # DONE
        'TRIVAGO|EXPEDIA|HOTELS|US|SPA|Magneto|3.7.3|PROD', # FAILED
        # 'TRIVAGO|EXPEDIA|HOTELS|UK|SPA|Magneto|3.7.3|PROD',
        # 'TRIVAGO|EXPEDIA|HOTELS|BR|SPA|Magneto|3.7.3|PROD',
        # 'TRIVAGO|EXPEDIA|HOTELS|HK|SPA|Magneto|3.7.3|PROD',
        # 'TRIVAGO|EXPEDIA|HOTELS|IT|SPA|Magneto|3.7.3|PROD',
        # 'TRIVAGO|EXPEDIA|HOTELS|NZ|SPA|Magneto|3.7.3|PROD',
        # 'TRIVAGO|EXPEDIA|HOTELS|DE|SPA|Magneto|3.7.3|PROD',
        # 'TRIVAGO|EXPEDIA|HOTELS|ES|SPA|Magneto|3.7.3|PROD',
        # 'TRIVAGO|EXPEDIA|HOTELS|NO|SPA|4Rank|1.1.EtaC|PROD',
        # 'TRIVAGO|EXPEDIA|HOTELS|KR|SPA|4Rank|1.1.EtaC|PROD',
        # 'TRIVAGO|HCOM|HOTELS|UK|SPA|Magneto|3.7.3|PROD',
        'TRIVAGO|HCOM|HOTELS|US|SPA|Magneto|3.7.3|PROD',
        # 'TRIVAGO|HCOM|HOTELS|HK|SPA|Magneto|3.7.3|PROD',
        # 'TRIVAGO|HCOM|HOTELS|IT|SPA|Magneto|3.7.3|PROD',
        # 'TRIVAGO|HCOM|HOTELS|ES|SPA|Magneto|3.7.3|PROD',
        # 'TRIVAGO|HCOM|HOTELS|BR|SPA|Magneto|3.7.3|PROD',
        # 'TRIVAGO|HCOM|HOTELS|DE|SPA|Magneto|3.7.3|PROD',
        # 'TRIVAGO|HCOM|HOTELS|TR|SPA|Magneto|2.1.7|PROD',
        # 'TRIVAGO|HCOM|HOTELS|CA|SPA|Magneto|3.3.1|PROD',
        # 'TRIVAGO|HCOM|HOTELS|FR|SPA|Magneto|3.3.1|PROD',
    ]

    migrator = RuleMigrator()
    migrator.run(targets)


if __name__ == "__main__":
    main()
    print("Rule migration completed.")
