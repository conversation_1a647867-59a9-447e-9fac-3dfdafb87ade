"""
Main entry point for Loki Migration Tool
Orchestrates different types of migrations (Rules, Bid Unit Configs, etc.)
"""
import logging
from typing import List

from migrators.rule_migrator import RuleMigrator
from migrators.bid_unit_config_migrator import BidUnitConfigMigrator

# Set up logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("LokiMigrationTool")


def run_rule_migration(targets: List[str]) -> None:
    """
    Run rule migration for the specified targets

    Args:
        targets: List of target strings to migrate rules for
    """
    logger.info("Starting Rule Migration")
    rule_migrator = RuleMigrator()
    rule_migrator.run(targets)
    logger.info("Rule Migration completed")


def run_bid_unit_config_migration(targets: List[str]) -> None:
    """
    Run bid unit configuration migration for the specified targets

    Args:
        targets: List of target strings to migrate bid unit configs for
    """
    logger.info("Starting Bid Unit Config Migration")
    bid_unit_migrator = BidUnitConfigMigrator()
    bid_unit_migrator.run(targets)
    logger.info("Bid Unit Config Migration completed")


def run_all_migrations(targets: List[str]) -> None:
    """
    Run all available migrations for the specified targets

    Args:
        targets: List of target strings to migrate
    """
    logger.info("Starting All Migrations")

    # Run rule migration
    run_rule_migration(targets)

    # Run bid unit config migration
    run_bid_unit_config_migration(targets)

    logger.info("All Migrations completed")


def main():
    """
    Main function to orchestrate different types of migrations

    You can choose to run:
    1. Only rule migration
    2. Only bid unit config migration
    3. All migrations
    """

    # Example targets for different partners
    google_targets = [
        # 'GOOGLE|WOTIF|HOTELS|WTF:H:AU:$:CPC:MOBILE:X|WTF:AU:$:CPC:MOBILE:X:ALL:ALL:ALL:PROMOTED|Magneto|3.3.1|BAU',
        # 'GOOGLE|WOTIF|HOTELS|WTF:H:AU:$:CPC:DESKTOP:X|WTF:AU:$:CPC:DESKTOP:X:ALL:ALL:ALL:PROMOTED|Magneto|3.3.1|BAU',
    ]

    tripadvisor_targets = [
        # 'TRIPADVISOR|HCOM|HOTELS|AU|DESKTOP|Magneto|3.3.1|BAU'
    ]

    trivago_targets = [
        # 'TRIVAGO|EXPEDIA|HOTELS|US|SPA|Magneto|3.7.3|PROD',
        # 'TRIVAGO|HCOM|HOTELS|US|SPA|Magneto|3.7.3|PROD',
        'TRIVAGO|EXPEDIA|HOTELS|US|SPA|Magneto|3.7.3|PROD',
        'TRIVAGO|HCOM|HOTELS|US|SPA|Magneto|3.7.3|PROD',
    ]

    # Choose which targets to use
    targets = trivago_targets

    # Choose which migration(s) to run:

    # Option 1: Run only rule migration
    run_rule_migration(targets)

    # Option 2: Run only bid unit config migration
    # run_bid_unit_config_migration(targets)

    # Option 3: Run all migrations
    # run_all_migrations(targets)

    logger.info("Migration process completed.")


if __name__ == "__main__":
    main()