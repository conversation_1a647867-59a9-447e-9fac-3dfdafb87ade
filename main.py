"""
Main entry point for Loki Migration Tool
Orchestrates different types of migrations (Rules, Bid Unit Configs, etc.)
"""
import logging
from typing import List

from migrators.rule_migrator import RuleMigrator
from migrators.bid_unit_config_migrator import BidUnitConfigMigrator
from services.deepthought_service import DeepThoughtService
from config.settings import Settings

# Set up logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("LokiMigrationTool")


def run_rule_migration(targets: List[str]) -> None:
    """
    Run rule migration for the specified targets

    Args:
        targets: List of target strings to migrate rules for
    """
    logger.info("Starting Rule Migration")
    rule_migrator = RuleMigrator()
    rule_migrator.run(targets)
    logger.info("Rule Migration completed")


def run_bid_unit_config_migration(targets: List[str]) -> None:
    """
    Run bid unit configuration migration for the specified targets

    Args:
        targets: List of target strings to migrate bid unit configs for
    """
    logger.info("Starting Bid Unit Config Migration")
    bid_unit_migrator = BidUnitConfigMigrator()
    bid_unit_migrator.run(targets)
    logger.info("Bid Unit Config Migration completed")


def run_all_migrations(targets: List[str]) -> None:
    """
    Run all available migrations for the specified targets

    Args:
        targets: List of target strings to migrate
    """
    logger.info("Starting All Migrations")

    # Run rule migration
    run_rule_migration(targets)

    # Run bid unit config migration
    run_bid_unit_config_migration(targets)

    logger.info("All Migrations completed")


def discover_available_targets():
    """
    Discover available targets from DTUI instead of hardcoding them

    Returns:
        List of available target strings
    """
    logger.info("Discovering available targets from DTUI...")

    settings = Settings()
    dt_service = DeepThoughtService(base_url=settings.dtui_api_url)

    # Discover Trivago targets
    trivago_targets = dt_service.discover_trivago_targets()

    # TODO: Add discovery for other partners (Google, TripAdvisor) if needed
    # google_targets = dt_service.discover_google_targets()
    # tripadvisor_targets = dt_service.discover_tripadvisor_targets()

    all_targets = trivago_targets
    logger.info(f"Discovered {len(all_targets)} available targets")

    return all_targets


def main():
    """
    Main function to orchestrate different types of migrations

    Dynamically discovers available targets from DTUI and runs migrations
    """

    # Discover available targets dynamically from DTUI
    targets = discover_available_targets()

    if not targets:
        logger.warning("No available targets found. Exiting.")
        return

    logger.info(f"Found {len(targets)} targets to process:")
    for i, target in enumerate(targets, 1):
        logger.info(f"  {i}. {target}")

    # You can filter targets here if needed
    # For example, only process EXPEDIA targets:
    # targets = [t for t in targets if 'EXPEDIA' in t]

    # Choose which migration(s) to run:

    # Option 1: Run only rule migration
    run_rule_migration(targets)

    # Option 2: Run only bid unit config migration
    # run_bid_unit_config_migration(targets)

    # Option 3: Run all migrations
    # run_all_migrations(targets)

    logger.info("Migration process completed.")


if __name__ == "__main__":
    main()