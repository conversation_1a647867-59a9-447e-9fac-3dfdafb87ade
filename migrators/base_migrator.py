"""
Base migrator class providing common functionality for all migrators
"""
import logging
import os
import time
from abc import ABC, abstractmethod
from typing import List

from config.settings import Settings
from services.loki_service import LokiService
from services.deepthought_service import DeepThoughtService


class BaseMigrator(ABC):
    """Abstract base class for all migrators providing common functionality"""

    def __init__(self, migrator_name: str):
        """
        Initialize the base migrator with common services and settings
        
        Args:
            migrator_name: Name of the migrator for logging purposes
        """
        self.settings = Settings()
        self.migrator_name = migrator_name
        self.main_logger = logging.getLogger(f"LokiMigrationTool.{migrator_name}")

        # Initialize common services
        self.loki_service = LokiService(
            self.settings,
            server_address=self.settings.loki_server_address,
            use_ssl=self.settings.loki_use_ssl,
            timeout_seconds=60
        )
        self.dt_service = DeepThoughtService(
            base_url=self.settings.dtui_api_url
        )

        # Initialize counters
        self.item_count = 0
        self.updated_count = 0
        self.created_count = 0
        self.no_change_count = 0
        self.failed_count = 0

        # Create logs directory if it doesn't exist
        os.makedirs("logs", exist_ok=True)

    @abstractmethod
    def process_target(self, target_string: str) -> None:
        """
        Process a single target string - must be implemented by subclasses
        
        Args:
            target_string: The target string to process
        """
        pass

    def run(self, targets: List[str]) -> None:
        """
        Run the migration for all targets
        
        Args:
            targets: List of target strings to process
        """
        start_time = time.time()
        self.main_logger.info(f"Starting {self.migrator_name} migration process for {len(targets)} targets")

        try:
            with self.loki_service.create_channel() as channel:
                for target_string in targets:
                    try:
                        self.process_target(target_string)
                    except Exception as e:
                        self.main_logger.error(f"Error processing target {target_string}: {e}", exc_info=True)
                        self.main_logger.info(f"Continuing with remaining targets...")

            if self.settings.dry_run:
                self.main_logger.info(f"Dry run completed successfully, results can be viewed at: ./dry_run_output")
        except Exception as e:
            self.main_logger.error(f"Error during {self.migrator_name} migration: {e}", exc_info=True)

        elapsed_time = time.time() - start_time
        self.main_logger.info(f"{self.migrator_name} migration completed in {elapsed_time:.2f} seconds.")
        self.main_logger.info(f"Total items processed: {self.item_count}")
        
        if not self.settings.dry_run:
            self.main_logger.info(f"Created: {self.created_count}, Updated: {self.updated_count}, "
                                  f"No changes: {self.no_change_count}, Failed: {self.failed_count}")

    def reset_counters(self) -> None:
        """Reset all counters to zero"""
        self.item_count = 0
        self.updated_count = 0
        self.created_count = 0
        self.no_change_count = 0
        self.failed_count = 0
