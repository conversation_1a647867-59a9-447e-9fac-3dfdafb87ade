"""
Bid Unit Config Migrator - Transfers bid unit configurations from DeepThought to Loki
"""
import logging
import os
from typing import List

from .base_migrator import BaseMigrator
from models.target import TABidTarget, GHABidTarget, TVGBidTarget
from models.bid_unit_config import CreateBidUnitConfigRequest, ExperimentConfig
from utils.logging_utils import setup_target_logger


class BidUnitConfigMigrator(BaseMigrator):
    """Migrator class that orchestrates the bid unit configuration migration process"""

    def __init__(self):
        super().__init__("BidUnitConfigMigrator")
        
        # Initialize bid unit config-specific services here if needed
        # self.bid_unit_service = BidUnitService(...)

    def process_target(self, target_string: str) -> None:
        """Process a single target string for bid unit configuration migration"""
        # Create a safe filename for the log file
        target_logger, file_handler = setup_target_logger(target_string)

        # Set the logger for the services for this target
        self.loki_service.set_logger(target_logger)
        self.dt_service.set_logger(target_logger)

        # Log to both the main logger and the target-specific logger
        self.main_logger.info(f"Processing bid unit config target: {target_string}")
        target_logger.info(f"Starting bid unit config processing for target: {target_string}")

        try:
            # Parse target
            if "TRIPADVISOR" in target_string:
                target = TABidTarget.from_pipe_string(target_string)
                # TODO: Implement bid unit config fetching for TripAdvisor
                # bid_unit_configs = self.dt_service.fetch_ta_bid_unit_configs(...)
            elif "TRIVAGO" in target_string:
                target = TVGBidTarget.from_pipe_string(target_string)
                # TODO: Implement bid unit config fetching for Trivago
                # bid_unit_configs = self.dt_service.fetch_tvg_bid_unit_configs(...)
            elif "GOOGLE" in target_string:
                target = GHABidTarget.from_pipe_string(target_string)
                # TODO: Implement bid unit config fetching for Google Hotel Ads
                # bid_unit_configs = self.dt_service.fetch_gha_bid_unit_configs(...)
            else:
                error_msg = f"Unsupported target type: {target_string}"
                self.main_logger.error(error_msg)
                target_logger.error(error_msg)
                file_handler.close()
                target_logger.removeHandler(file_handler)
                return

            # TODO: Implement the bid unit config migration logic
            target_logger.info("Bid unit config migration logic to be implemented")

            # Example structure for processing bid unit configs:
            # 1. Fetch bid unit configs from DeepThought
            # 2. Transform configs to Loki format using CreateBidUnitConfigRequest
            # 3. Create or update configs in Loki
            # 4. Handle dry run mode

            # Example of how to use the CreateBidUnitConfigRequest data class:
            # experiment_config = ExperimentConfig(
            #     bin_id="example_bin",
            #     experiment_id="example_exp",
            #     experiment_name="Example Experiment"
            # )
            #
            # bid_unit_request = CreateBidUnitConfigRequest(
            #     assembler_rule_ids=["rule1", "rule2"],
            #     bid_unit_id="example_bid_unit",
            #     experiment_config=experiment_config,
            #     model_id="example_model",
            #     model_rule_ids=["model_rule1"],
            #     type=3
            # )
            #
            # # Convert to dict for API calls
            # request_dict = bid_unit_request.to_dict()
            # target_logger.info(f"Would send request: {request_dict}")
            
            if self.settings.dry_run:
                filename = f"./{self.settings.dry_run_output_folder}/bid_unit_configs_{target_string.replace('|', '_')}.csv"
                # Create the directory if it doesn't exist
                os.makedirs(self.settings.dry_run_output_folder, exist_ok=True)
                # TODO: Write bid unit config data to CSV
                target_logger.info(f"Dry run: would write bid unit config output to {filename}")
            else:
                # Log summary for actual migration
                target_logger.info(f"Bid unit config target processing complete. Items: {self.item_count}, "
                                   f"Created: {self.created_count}, Updated: {self.updated_count}, "
                                   f"No changes: {self.no_change_count}, Failed: {self.failed_count}")

        except Exception as e:
            error_msg = f"Error processing bid unit config target {target_string}: {e}"
            self.main_logger.error(error_msg, exc_info=True)
            target_logger.error(error_msg, exc_info=True)

        finally:
            # Close and remove the file handler
            target_logger.info(f"Finished processing bid unit config target: {target_string}")
            file_handler.close()
            target_logger.removeHandler(file_handler)

            # Reset the service loggers to the default
            self.loki_service.reset_logger()
            self.dt_service.reset_logger()
