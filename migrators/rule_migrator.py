"""
Rule Migrator - Transfers rules from DeepThought to Loki
"""
import os

from .base_migrator import BaseMigrator
from models.rule import RuleItem
from services.rule_generator import RuleGenerator
from models.target import TABidTarget, GHABidTarget, TVGBidTarget
from utils.builders import build_rule_item_payload
from utils.logging_utils import setup_target_logger


class RuleMigrator(BaseMigrator):
    """Migrator class that orchestrates the rule migration process"""

    def __init__(self):
        super().__init__("RuleMigrator")
        
        # Initialize rule-specific services
        self.rule_generator = RuleGenerator(
            loki_service=self.loki_service, 
            deepthought_service=self.dt_service, 
            settings=self.settings
        )

    def process_target(self, target_string: str) -> None:
        """Process a single target string"""
        # Create a safe filename for the log file
        target_logger, file_handler = setup_target_logger(target_string)

        # Set the logger for the loki service for this target
        self.loki_service.set_logger(target_logger)
        self.dt_service.set_logger(target_logger)

        # Log to both the main logger and the target-specific logger
        self.main_logger.info(f"Processing target: {target_string}")
        target_logger.info(f"Starting processing for target: {target_string}")

        try:
            # Parse target
            if "TRIPADVISOR" in target_string:
                target = TABidTarget.from_pipe_string(target_string)
                fbls = self.dt_service.fetch_ta_fbls(
                    lob=target.lob,
                    partner=target.partner,
                    brand=target.brand,
                    algoName=target.model_name,
                    algoVersion=target.model_version,
                    posa=target.posa,
                    device=target.device
                )
            elif "TRIVAGO" in target_string:
                target = TVGBidTarget.from_pipe_string(target_string)
                fbls = self.dt_service.fetch_tvg_fbls(
                    lob=target.lob,
                    partner=target.partner,
                    brand=target.brand,
                    algoName=target.model_name,
                    algoVersion=target.model_version,
                    posa=target.posa,
                    auction=target.auction_type,
                    testName=target.test_name,
                    bucket=target.bucket
                )
            elif "GOOGLE" in target_string:
                target = GHABidTarget.from_pipe_string(target_string)
                fbls = self.dt_service.fetch_gha_fbls(
                    lob=target.lob,
                    partner=target.partner,
                    brand=target.brand,
                    algoName=target.model_name,
                    algoVersion=target.model_version,
                    account=target.account,
                    campaign=target.campaign
                )
            else:
                error_msg = f"Unsupported target type: {target_string}"
                self.main_logger.error(error_msg)
                target_logger.error(error_msg)
                file_handler.close()
                target_logger.removeHandler(file_handler)
                return

            # Fetch global FBLs
            target_logger.info(f"Fetching global FBLs for {target.brand}, {target.lob}, {target.partner}")
            global_fbls = self.dt_service.fetch_global_fbls(
                brand_name=target.brand,
                channel="META",
                lob=target.lob,
                partner=target.partner
            )

            # Generate rule items
            target_logger.info(f"Generating rule items")
            df_rule_set = self.rule_generator.generate_rule_items(
                target=target,
                fbls=fbls,
                global_fbls=global_fbls,
                exclude_factors_of_one=self.settings.exclude_factors_of_one
            )
            target_logger.info(f"Generated {len(df_rule_set)} rule items")

            # Create the rule in Loki
            try:
                labels = {
                    "lob": target.lob,
                    "partner": target.partner,
                    "brand": target.brand,
                }

                # Add posa field for targets that have it (TripAdvisor and Trivago)
                if hasattr(target, 'posa'):
                    labels["posa"] = target.posa

                # Add auction field for targets that have it (Trivago)
                if hasattr(target, 'auction_type'):
                    labels["auction"] = target.auction_type

                grouped_rule_sets = df_rule_set.groupby('rule_name')
                for rule_name, df_rule_items in grouped_rule_sets:
                    rule_id = None
                    rule_name = f"{rule_name}".strip()
                    rule_payload = {
                        'rule_attachment_type': self.settings.attachment_type,
                        'labels': labels,
                        'marketing_channel': self.settings.marketing_channels.get(target.partner),
                        'name': rule_name,
                        'schema_id': self.settings.schema_id
                    }

                    if not self.settings.dry_run:
                        try:
                            target_logger.info(f"Getting or creating rule: {rule_name}")
                            rule_id = self.loki_service.get_or_create_rule(**rule_payload)
                            target_logger.info(f"Rule ID: {rule_id}")
                        except Exception as e:
                            error_msg = f"Error creating rule {rule_name}: {e}"
                            self.main_logger.error(error_msg, exc_info=True)
                            target_logger.error(error_msg, exc_info=True)
                            raise e
                    else:
                        rule_id = "dry_run"
                        target_logger.info(f"Dry run: would create/get rule {rule_name}")

                    rule_items = [RuleItem(**item) for item in df_rule_items.to_dict(orient='records')]
                    target_logger.info(f"Processing {len(rule_items)} rule items")

                    for item in rule_items:
                        self.item_count += 1

                        item_payload = build_rule_item_payload(
                            item=item,
                            business_case=target.business_use_case,
                            rule_id=rule_id
                        )
                        if not self.settings.dry_run:
                            result_id, action = self.loki_service.create_or_update_rule_item(**item_payload)

                            if action == 'created':
                                self.created_count += 1
                                msg = f"Created rule item {item.priority}: {item.resource} {item.operator} (ID: {result_id})"
                                self.main_logger.info(msg)
                                target_logger.info(msg)
                            elif action == 'updated':
                                self.updated_count += 1
                                msg = f"Updated rule item {item.priority}: {item.resource} {item.operator} (ID: {result_id})"
                                self.main_logger.info(msg)
                                target_logger.info(msg)
                            elif action == 'no_change':
                                self.no_change_count += 1
                                msg = f"No changes for rule item {item.priority}: {item.resource} {item.operator} (ID: {result_id})"
                                self.main_logger.info(msg)
                                target_logger.info(msg)
                            elif action == 'failed':
                                self.failed_count += 1
                                msg = f"Failed to create or update rule item {item.priority}: {item.resource} {item.operator}"
                                self.main_logger.error(msg)
                                target_logger.error(msg)
                            else:
                                msg = f"Unknown action {action} for rule item {item.priority}: {item.resource} {item.operator}"
                                self.main_logger.error(msg)
                                target_logger.error(msg)
                        else:
                            msg = f"Would have created/updated rule item {item.priority}: {item.resource} {item.operator} {item.dimensions}"
                            self.main_logger.info(msg)
                            target_logger.info(msg)

            except Exception as e:
                error_msg = f"Error processing rule {target_string}: {e}"
                self.main_logger.error(error_msg, exc_info=True)
                target_logger.error(error_msg, exc_info=True)

            if self.settings.dry_run:
                filename = f"./{self.settings.dry_run_output_folder}/{target_string.replace('|', '_')}.csv"
                # Create the directory if it doesn't exist
                os.makedirs(self.settings.dry_run_output_folder, exist_ok=True)
                df_rule_set.to_csv(filename, index=False)
                target_logger.info(f"Wrote dry run output to {filename}")
            else:
                # Log summary only displayed when not running in dry run mode
                target_logger.info(f"Target processing complete. Items: {self.item_count}, Created: {self.created_count}, "
                                   f"Updated: {self.updated_count}, No changes: {self.no_change_count}, "
                                   f"Failed: {self.failed_count}")
        finally:
            # Close and remove the file handler
            target_logger.info(f"Finished processing target: {target_string}")
            file_handler.close()
            target_logger.removeHandler(file_handler)

            # Reset the loki service logger to the default
            self.loki_service.reset_logger()
            self.dt_service.reset_logger()
