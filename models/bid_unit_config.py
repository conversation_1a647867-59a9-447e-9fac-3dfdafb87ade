"""
Data models for Bid Unit Configuration requests and responses
"""
from dataclasses import dataclass
from typing import List, Optional


@dataclass
class ExperimentConfig:
    """Configuration for experiment settings"""
    bin_id: str
    experiment_id: str
    experiment_name: str


@dataclass
class CreateBidUnitConfigRequest:
    """Request model for creating bid unit configurations in Loki"""
    assembler_rule_ids: List[str]
    bid_unit_id: str
    experiment_config: ExperimentConfig
    model_id: str
    model_rule_ids: List[str]
    type: int

    @classmethod
    def from_dict(cls, data: dict) -> 'CreateBidUnitConfigRequest':
        """
        Create a CreateBidUnitConfigRequest from a dictionary
        
        Args:
            data: Dictionary containing the request data
            
        Returns:
            CreateBidUnitConfigRequest instance
        """
        experiment_config = ExperimentConfig(**data['experiment_config'])
        
        return cls(
            assembler_rule_ids=data['assembler_rule_ids'],
            bid_unit_id=data['bid_unit_id'],
            experiment_config=experiment_config,
            model_id=data['model_id'],
            model_rule_ids=data['model_rule_ids'],
            type=data['type']
        )

    def to_dict(self) -> dict:
        """
        Convert the request to a dictionary suitable for JSON serialization
        
        Returns:
            Dictionary representation of the request
        """
        return {
            'assembler_rule_ids': self.assembler_rule_ids,
            'bid_unit_id': self.bid_unit_id,
            'experiment_config': {
                'bin_id': self.experiment_config.bin_id,
                'experiment_id': self.experiment_config.experiment_id,
                'experiment_name': self.experiment_config.experiment_name
            },
            'model_id': self.model_id,
            'model_rule_ids': self.model_rule_ids,
            'type': self.type
        }


# Example usage and validation
if __name__ == "__main__":
    # Example JSON data
    example_data = {
        "assembler_rule_ids": [
            "exercitation magna qui nulla deserunt",
            "exercitation occaecat laboris culpa laborum"
        ],
        "bid_unit_id": "irure est dolor",
        "experiment_config": {
            "bin_id": "ex commodo aute sint enim",
            "experiment_id": "veniam esse et",
            "experiment_name": "consectetur ad"
        },
        "model_id": "anim",
        "model_rule_ids": [
            "ipsum commodo"
        ],
        "type": 3
    }
    
    # Create instance from dictionary
    request = CreateBidUnitConfigRequest.from_dict(example_data)
    print("Created request:", request)
    
    # Convert back to dictionary
    result_dict = request.to_dict()
    print("Converted back to dict:", result_dict)
    
    # Verify round-trip conversion
    assert result_dict == example_data
    print("✅ Round-trip conversion successful!")
