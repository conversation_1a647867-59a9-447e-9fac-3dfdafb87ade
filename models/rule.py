from dataclasses import dataclass
from typing import Optional


@dataclass
class Labels:
    auction: str
    brand: str
    lob: str
    partner: str
    posa: str


@dataclass
class Timestamp:
    seconds: str
    nanos: int


@dataclass
class Rule:
    labels: Labels
    id: str
    name: str
    marketing_channel: str
    schema_id: str
    attachment_type: str
    status: str
    created_by: str
    created_at: Timestamp
    updated_by: str
    updated_at: Optional[Timestamp]


@dataclass
class RuleItem:
    rule_name: str
    priority: int
    resource: str
    is_global: str
    operator: str
    factor: float
    minimum: int
    maximum: int
    dimensions: list

