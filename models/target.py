from dataclasses import dataclass, field
from typing import List, Optional, Any, ClassVar, Dict, Type, TypeVar, cast
from abc import ABC, abstractmethod


# Define a type variable for self-referential return type_definitions
T = TypeVar('T', bound='BaseBidTarget')


@dataclass
class BaseBidTarget(ABC):
    """Abstract base class for bid targets that can be extended for different partners."""

    # Common fields across all partner type_definitions
    partner: str
    brand: str
    lob: str

    # Store the original parts list internally
    parts: List[str] = field(repr=False)

    # Common index constants
    IDX_PARTNER: ClassVar[int] = 0
    IDX_BRAND: ClassVar[int] = 1
    IDX_LOB: ClassVar[int] = 2

    @classmethod
    @abstractmethod
    def get_field_indices(cls) -> Dict[str, int]:
        """Return a mapping of field names to their indices in the parts list."""
        pass

    @classmethod
    def from_parts(cls: Type[T], parts: List[str]) -> T:
        """
        Create a BidTarget object from a list of parts.

        Args:
            parts: List of string parts (split on | character)

        Returns:
            A StringParts object with all fields populated
        """
        field_indices = cls.get_field_indices()
        kwargs = {'parts': parts}

        for field_name, idx in field_indices.items():
            kwargs[field_name] = parts[idx] if len(parts) > idx else ""

        return cls(**kwargs)

    @classmethod
    def from_pipe_string(cls: Type[T], pipe_string: str) -> T:
        """
        Create a BidTarget object from a pipe-delimited string.

        Args:
            pipe_string: String with parts separated by |

        Returns:
            A BidTarget object with all fields populated
        """
        parts = pipe_string.split('|')
        return cls.from_parts(parts)

    def get_part(self, index: int, default: Any = None) -> Any:
        """
        Get a part by index with an optional default value.

        Args:
            index: The index of the part to retrieve
            default: Value to return if index is out of range

        Returns:
            The part at the given index or the default value
        """
        if 0 <= index < len(self.parts):
            return self.parts[index]
        return default


@dataclass
class GHABidTarget(BaseBidTarget):
    """GHA-specific implementation using account and campaign fields."""

    # GHA-specific fields
    account: str
    campaign: str
    model_name: str
    model_version: str
    business_use_case: str

    # GHA-specific indices
    IDX_ACCOUNT: ClassVar[int] = 3
    IDX_CAMPAIGN: ClassVar[int] = 4
    IDX_MODEL_NAME: ClassVar[int] = 5
    IDX_MODEL_VERSION: ClassVar[int] = 6
    IDX_BUSINESS_USE_CASE: ClassVar[int] = 7

    @classmethod
    def get_field_indices(cls) -> Dict[str, int]:
        """
            Return indices specific to GHA fields.
            Ex: 'GOOGLE|WOTIF|HOTELS|WTF:H:AU:$:CPC:MOBILE:X|WTF:AU:$:CPC:MOBILE:X:ALL:ALL:ALL:PROMOTED|Magneto|3.3.1|BAU',
        """
        return {
            'partner': cls.IDX_PARTNER,
            'brand': cls.IDX_BRAND,
            'lob': cls.IDX_LOB,
            'account': cls.IDX_ACCOUNT,
            'campaign': cls.IDX_CAMPAIGN,
            'model_name': cls.IDX_MODEL_NAME,
            'model_version': cls.IDX_MODEL_VERSION,
            'business_use_case': cls.IDX_BUSINESS_USE_CASE,
        }


@dataclass
class TABidTarget(BaseBidTarget):
    """TA-specific implementation using posa and device fields."""

    # TA-specific fields
    posa: str
    device: str
    model_name: str
    model_version: str
    business_use_case: str

    # TA-specific indices
    IDX_POSA: ClassVar[int] = 3
    IDX_DEVICE: ClassVar[int] = 4
    IDX_MODEL_NAME: ClassVar[int] = 5
    IDX_MODEL_VERSION: ClassVar[int] = 6
    IDX_BUSINESS_USE_CASE: ClassVar[int] = 7

    @classmethod
    def get_field_indices(cls) -> Dict[str, int]:
        """
            Return indices specific to TA fields.
            Ex: TRIPADVISOR|WOTIF|HOTELS|AU|DESKTOP|Magneto|3.3.1|BAU
        """
        return {
            'partner': cls.IDX_PARTNER,
            'brand': cls.IDX_BRAND,
            'lob': cls.IDX_LOB,
            'posa': cls.IDX_POSA,
            'device': cls.IDX_DEVICE,
            'model_name': cls.IDX_MODEL_NAME,
            'model_version': cls.IDX_MODEL_VERSION,
            'business_use_case': cls.IDX_BUSINESS_USE_CASE,
        }


@dataclass
class TVGBidTarget(BaseBidTarget):
    """TRIVAGO-specific implementation using posa and auction fields."""

    # TRIVAGO-specific fields
    posa: str
    auction: str
    model_name: str
    model_version: str
    business_use_case: str

    # TRIVAGO-specific indices
    IDX_POSA: ClassVar[int] = 3
    IDX_AUCTION: ClassVar[int] = 4
    IDX_MODEL_NAME: ClassVar[int] = 5
    IDX_MODEL_VERSION: ClassVar[int] = 6
    IDX_BUSINESS_USE_CASE: ClassVar[int] = 7

    @classmethod
    def get_field_indices(cls) -> Dict[str, int]:
        """
            Return indices specific to TRIVAGO fields.
            Ex: TRIVAGO|WOTIF|HOTELS|AU|SPA|Magneto|3.3.1|BAU
        """
        return {
            'partner': cls.IDX_PARTNER,
            'brand': cls.IDX_BRAND,
            'lob': cls.IDX_LOB,
            'posa': cls.IDX_POSA,
            'auction': cls.IDX_AUCTION,
            'model_name': cls.IDX_MODEL_NAME,
            'model_version': cls.IDX_MODEL_VERSION,
            'business_use_case': cls.IDX_BUSINESS_USE_CASE,
        }
