import logging

import requests
import urllib3

logger = logging.getLogger(__name__)

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class DeepThoughtService:
    def __init__(self, base_url):
        self.base_url = base_url
        self.logger = logger

    def set_logger(self, new_logger):
        """Set a new logger for this service instance"""
        self.logger = new_logger

    def reset_logger(self):
        """Reset to the default logger"""
        self.logger = logger

    def fetch(self, endpoint: str, params: dict = None):
        """
        General fetch method to interact with the API.
        """
        url = f"{self.base_url}{endpoint}"
        response = requests.get(url, params=params, verify=False)
        return response

    def fetch_gha_fbls(self, lob, partner, brand, algoName, algoVersion, account, campaign):
        params = {
            "channel": "meta",
            "lob": lob,
            "partner": partner,
            "brand": brand,
            "algoName": algoName,
            "algoVersion": algoVersion,
            "account": account,
            "campaign": campaign,
        }

        try:
            response = self.fetch(endpoint="/campaignStore/allFbl", params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.HTTPError as e:
            self.logger.error(f"HTTP error fetching FBLs: {e}, Status code: {response.status_code}", exc_info=True)
            try:
                error_detail = response.json()
                self.logger.error(f"API error details: {error_detail}")
            except ValueError:
                self.logger.error(f"Response content: {response.text[:500]}")
            raise
        except Exception as e:
            # Other exceptions (connection errors, timeouts, JSON parsing)
            self.logger.error(f"Error fetching FBLs: {e}", exc_info=True)
            raise

    def fetch_ta_fbls(self, lob, partner, brand, algoName, algoVersion, posa, device):
        params = {
            "channel": "meta",
            "lob": lob,
            "partner": partner,
            "brand": brand,
            "algoName": algoName,
            "algoVersion": algoVersion,
            "posa": posa,
            "device": device,
        }

        try:
            response = self.fetch(endpoint="/campaignStore/allFbl", params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.HTTPError as e:
            self.logger.error(f"HTTP error fetching FBLs: {e}, Status code: {response.status_code}", exc_info=True)
            try:
                error_detail = response.json()
                self.logger.error(f"API error details: {error_detail}")
            except ValueError:
                self.logger.error(f"Response content: {response.text[:500]}")
            raise
        except Exception as e:
            # Other exceptions (connection errors, timeouts, JSON parsing)
            self.logger.error(f"Error fetching FBLs: {e}", exc_info=True)
            raise

    def fetch_tvg_fbls(self, lob, partner, brand, algoName, algoVersion, posa, auction):
        params = {
            "channel": "meta",
            "lob": lob,
            "partner": partner,
            "brand": brand,
            "algoName": algoName,
            "algoVersion": algoVersion,
            "posa": posa,
            "auction": auction,
        }

        try:
            response = self.fetch(endpoint="/campaignStore/allFbl", params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.HTTPError as e:
            self.logger.error(f"HTTP error fetching FBLs: {e}, Status code: {response.status_code}", exc_info=True)
            try:
                error_detail = response.json()
                self.logger.error(f"API error details: {error_detail}")
            except ValueError:
                self.logger.error(f"Response content: {response.text[:500]}")
            raise
        except Exception as e:
            # Other exceptions (connection errors, timeouts, JSON parsing)
            self.logger.error(f"Error fetching FBLs: {e}", exc_info=True)
            raise

    def fetch_global_fbls(self, brand_name, channel, lob, partner):
        params = {
            "brand": brand_name,
            "channel": channel,
            "lob": lob,
            "partner": partner,
        }
        try:
            response = self.fetch(endpoint="/campaignStore/allFbl/global", params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.HTTPError as e:
            self.logger.error(f"HTTP error fetching FBLs: {e}, Status code: {response.status_code}", exc_info=True)
            try:
                error_detail = response.json()
                self.logger.error(f"API error details: {error_detail}")
            except ValueError:
                self.logger.error(f"Response content: {response.text[:500]}")
            raise
        except Exception as e:
            # Other exceptions (connection errors, timeouts, JSON parsing)
            self.logger.error(f"Error fetching FBLs: {e}", exc_info=True)
            raise