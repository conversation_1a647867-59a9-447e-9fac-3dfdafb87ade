import logging

import requests
import urllib3

logger = logging.getLogger(__name__)

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class DeepThoughtService:
    def __init__(self, base_url):
        self.base_url = base_url
        self.logger = logger

    def set_logger(self, new_logger):
        """Set a new logger for this service instance"""
        self.logger = new_logger

    def reset_logger(self):
        """Reset to the default logger"""
        self.logger = logger

    def fetch(self, endpoint: str, params: dict = None):
        """
        General fetch method to interact with the API.
        """
        url = f"{self.base_url}{endpoint}"
        response = requests.get(url, params=params, verify=False)
        return response

    def fetch_gha_fbls(self, lob, partner, brand, algoName, algoVersion, account, campaign):
        params = {
            "channel": "meta",
            "lob": lob,
            "partner": partner,
            "brand": brand,
            "algoName": algoName,
            "algoVersion": algoVersion,
            "account": account,
            "campaign": campaign,
        }

        try:
            response = self.fetch(endpoint="/campaignStore/allFbl", params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.HTTPError as e:
            self.logger.error(f"HTTP error fetching FBLs: {e}, Status code: {response.status_code}", exc_info=True)
            try:
                error_detail = response.json()
                self.logger.error(f"API error details: {error_detail}")
            except ValueError:
                self.logger.error(f"Response content: {response.text[:500]}")
            raise
        except Exception as e:
            # Other exceptions (connection errors, timeouts, JSON parsing)
            self.logger.error(f"Error fetching FBLs: {e}", exc_info=True)
            raise

    def fetch_ta_fbls(self, lob, partner, brand, algoName, algoVersion, posa, device):
        params = {
            "channel": "meta",
            "lob": lob,
            "partner": partner,
            "brand": brand,
            "algoName": algoName,
            "algoVersion": algoVersion,
            "posa": posa,
            "device": device,
        }

        try:
            response = self.fetch(endpoint="/campaignStore/allFbl", params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.HTTPError as e:
            self.logger.error(f"HTTP error fetching FBLs: {e}, Status code: {response.status_code}", exc_info=True)
            try:
                error_detail = response.json()
                self.logger.error(f"API error details: {error_detail}")
            except ValueError:
                self.logger.error(f"Response content: {response.text[:500]}")
            raise
        except Exception as e:
            # Other exceptions (connection errors, timeouts, JSON parsing)
            self.logger.error(f"Error fetching FBLs: {e}", exc_info=True)
            raise

    def fetch_tvg_fbls(self, lob, partner, brand, algoName, algoVersion, posa, auction, testName="", bucket=""):
        params = {
            "channel": "meta",
            "lob": lob,
            "partner": partner,
            "brand": brand,
            "algoName": algoName,
            "algoVersion": algoVersion,
            "posa": posa,
            "auction": auction,
        }

        # Add optional parameters if provided
        if testName:
            params["testName"] = testName
        if bucket:
            params["bucket"] = bucket

        try:
            response = self.fetch(endpoint="/campaignStore/allFbl", params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.HTTPError as e:
            self.logger.error(f"HTTP error fetching FBLs: {e}, Status code: {response.status_code}", exc_info=True)
            try:
                error_detail = response.json()
                self.logger.error(f"API error details: {error_detail}")
            except ValueError:
                self.logger.error(f"Response content: {response.text[:500]}")
            raise
        except Exception as e:
            # Other exceptions (connection errors, timeouts, JSON parsing)
            self.logger.error(f"Error fetching FBLs: {e}", exc_info=True)
            raise

    def fetch_global_fbls(self, brand_name, channel, lob, partner):
        params = {
            "brand": brand_name,
            "channel": channel,
            "lob": lob,
            "partner": partner,
        }
        try:
            response = self.fetch(endpoint="/campaignStore/allFbl/global", params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.HTTPError as e:
            self.logger.error(f"HTTP error fetching FBLs: {e}, Status code: {response.status_code}", exc_info=True)
            try:
                error_detail = response.json()
                self.logger.error(f"API error details: {error_detail}")
            except ValueError:
                self.logger.error(f"Response content: {response.text[:500]}")
            raise
        except Exception as e:
            # Other exceptions (connection errors, timeouts, JSON parsing)
            self.logger.error(f"Error fetching FBLs: {e}", exc_info=True)
            raise

    def discover_trivago_targets(self, base_params=None):
        """
        Discover available Trivago targets by querying DTUI for available configurations.

        Args:
            base_params: Base parameters to use for discovery (optional)

        Returns:
            List of target strings that are available in DTUI
        """
        if base_params is None:
            base_params = {
                "channel": "meta",
                "lob": "HOTELS",
                "partner": "TRIVAGO"
            }

        # Common Trivago configurations to test
        test_configs = [
            {"brand": "EXPEDIA", "posa": "US", "auction": "SPA", "algoName": "Magneto", "algoVersion": "3.7.3"},
            {"brand": "HCOM", "posa": "US", "auction": "SPA", "algoName": "Magneto", "algoVersion": "3.7.3"},
            {"brand": "EXPEDIA", "posa": "UK", "auction": "SPA", "algoName": "Magneto", "algoVersion": "3.7.3"},
            {"brand": "HCOM", "posa": "UK", "auction": "SPA", "algoName": "Magneto", "algoVersion": "3.7.3"},
            {"brand": "WOTIF", "posa": "AU", "auction": "SPA", "algoName": "Magneto", "algoVersion": "3.6.1"},
            {"brand": "WOTIF", "posa": "NZ", "auction": "SPA", "algoName": "Magneto", "algoVersion": "3.7.3"},
        ]

        available_targets = []

        for config in test_configs:
            params = {**base_params, **config}

            try:
                self.logger.info(f"Testing configuration: {config}")
                response = self.fetch(endpoint="/campaignStore/allFbl", params=params)

                if response.status_code == 200:
                    data = response.json()

                    # Extract testName and bucket from response if available
                    test_name = data.get('testName', '')
                    bucket = data.get('bucket', '')

                    # Build target string
                    target_parts = [
                        "TRIVAGO",
                        config["brand"],
                        config["lob"] if "lob" in config else base_params["lob"],
                        config["posa"],
                        config["auction"],
                        config["algoName"],
                        config["algoVersion"],
                        "PROD",  # Default business use case
                        test_name,
                        bucket
                    ]

                    target_string = "|".join(target_parts)
                    available_targets.append(target_string)
                    self.logger.info(f"✅ Found available target: {target_string}")

                elif response.status_code == 404:
                    self.logger.debug(f"❌ Configuration not available: {config}")
                else:
                    self.logger.warning(f"⚠️ Unexpected response {response.status_code} for config: {config}")

            except Exception as e:
                self.logger.debug(f"❌ Error testing config {config}: {e}")
                continue

        self.logger.info(f"Discovery complete. Found {len(available_targets)} available targets")
        return available_targets