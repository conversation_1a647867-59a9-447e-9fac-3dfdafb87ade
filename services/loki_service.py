import json
import logging
import time
from typing import Dict, Any, Optional, Context<PERSON>ana<PERSON>, Generator, List, Tuple
from contextlib import contextmanager

import grpc
from grpc._channel import Channel
from marketing_pb.loki.v1 import rule_svc_pb2
import marketing_pb.loki.v1.rule_svc_pb2_grpc as loki_rule_svc_grpc
from marketing_pb.loki.v1.rule_svc_pb2 import RuleAttachmentType, GetRuleItemsResponse

from config.settings import Settings

logger = logging.getLogger(__name__)


class LokiService:
    """Service for interacting with Loki's gRPC API"""

    def __init__(self, settings: Settings, server_address: str, use_ssl: bool = True, timeout_seconds: int = 30):
        """
        Initialize the Loki service.

        Args:
            server_address: The address of the Loki gRPC server (host:port)
            use_ssl: Whether to use SSL for the connection
            timeout_seconds: Timeout for gRPC requests in seconds
        """
        self.settings = settings
        self.server_address = server_address
        self.use_ssl = use_ssl
        self.timeout_seconds = timeout_seconds
        self.logger = logger

    def set_logger(self, new_logger):
        """Set a new logger for this service instance"""
        self.logger = new_logger

    def reset_logger(self):
        """Reset to the default logger"""
        self.logger = logger

    @contextmanager
    def create_channel(self) -> Generator[Any, Any, None]:
        """
        Create and yield a gRPC channel to the Loki server.
        Automatically closes the channel when done.

        Returns:
            A context manager for the gRPC channel
        """
        channel = None
        try:
            if self.use_ssl:
                # For secure connection
                credentials = grpc.ssl_channel_credentials()
                channel = grpc.secure_channel(self.server_address, credentials)
            else:
                # For insecure connection (not recommended for production)
                channel = grpc.insecure_channel(self.server_address)

            yield channel
        finally:
            if channel:
                channel.close()

    def get_rule_items(self, rule_id: str) -> Optional[List[Dict[str, Any]]]:
        """
        Get all rule items for a specific rule.

        Args:
            rule_id: The ID of the parent rule

        Returns:
            List of rule items or None if there was an error
        """
        with self.create_channel() as channel:
            try:
                stub = loki_rule_svc_grpc.RuleServiceStub(channel)
                offset = 0
                limit = 100
                all_rule_items = []

                while True:
                    self.logger.info(f"Fetching rule items for ID: {rule_id}, offset: {offset}, limit: {limit}")

                    # Create a request with pagination parameters
                    request = rule_svc_pb2.GetRuleItemsRequest(
                        rule_id=rule_id,
                        offset=offset,
                        limit=limit
                    )
                    start_time = time.time()
                    response: GetRuleItemsResponse = stub.GetRuleItems(request, timeout=self.timeout_seconds)
                    latency = time.time() - start_time
                    self.logger.info(f"gRPC call latency: {latency:.3f} seconds")

                    # Process the items in this batch
                    batch_items = []
                    for item in response.items:
                        batch_items.append({
                            'id': item.id,
                            'business_case': item.business_case,
                            'info': item.info,
                            'rule_id': item.rule_id,
                            'version': int(item.version)
                        })
                    all_rule_items.extend(batch_items)
                    self.logger.info(f"Fetched {len(batch_items)} rule items in this batch")

                    # Check if we've received fewer items than the page size,
                    if len(batch_items) < limit:
                        break

                    offset += limit  # Move to the next page
                self.logger.info(f"Successfully fetched all {len(all_rule_items)} rule items for rule ID: {rule_id}")
                return all_rule_items

            except grpc.RpcError as e:
                self.logger.error(f"gRPC error fetching rule items: {e.code()}: {e.details()}")
                return None
            except Exception as e:
                self.logger.error(f"Error fetching rule items: {e}", exc_info=True)
                return None

    def find_existing_rule_item(self, rule_id: str, business_case: str, info: Dict[str, Any] | str) -> dict[str, Any] | None:
        """
        Find an existing rule item that matches the given criteria.

        Args:
            rule_id: The ID of the parent rule
            business_case: The business case to match
            info: The rule item information to match

        Returns:
            The ID of the existing rule item, or None if not found
        """
        existing_items = self.get_rule_items(rule_id)
        if not existing_items:
            return None

        # Convert info to JSON string for comparison if it's a dictionary
        if isinstance(info, dict):
            info_str = json.dumps(info, sort_keys=True)
        else:
            info_str = info

        for item in existing_items:
            if item['business_case'] == business_case:
                # Compare the info content
                try:
                    # Try to parse existing info as JSON for comparison
                    existing_info = json.loads(item['info'])
                    if isinstance(info, dict):
                        if existing_info == info:
                            return item
                    else:
                        if json.dumps(existing_info, sort_keys=True) == info_str:
                            return item
                except (json.JSONDecodeError, TypeError):
                    # If existing info is not JSON, do string comparison
                    if item['info'] == info_str:
                        return item

        return None

    def update_rule_item(
            self,
            rule_item_id: str,
            business_case: str,
            info: Dict[str, Any] | str,
            version: str
    ) -> bool:
        """
        Update an existing rule item in Loki.

        Args:
            rule_item_id: The ID of the rule item to update
            business_case: The business case for the rule item
            info: The rule item information, either as a dictionary or JSON string
            rule_id: The ID of the parent rule

        Returns:
            True if update was successful, False otherwise
        """
        with self.create_channel() as channel:
            try:
                stub = loki_rule_svc_grpc.RuleServiceStub(channel)

                # Convert info to JSON string if it's a dictionary
                if isinstance(info, dict):
                    info = json.dumps(info)

                payload = {
                    "id": rule_item_id,
                    "business_case": business_case,
                    "info": info,
                    "version": version,
                }

                self.logger.info(f"Updating rule item with ID: {rule_item_id}")
                response = stub.UpdateRuleItem(
                    rule_svc_pb2.UpdateRuleItemRequest(**payload),
                    timeout=self.timeout_seconds
                )

                self.logger.info(f"Successfully updated rule item with ID: {rule_item_id}")
                return True

            except grpc.RpcError as e:
                self.logger.error(f"gRPC error updating rule item: {e.code()}: {e.details()}")
                return False
            except Exception as e:
                self.logger.error(f"Error updating rule item: {e}", exc_info=True)
                return False

    def create_rule(
            self,
            rule_attachment_type: int,
            labels: Dict[str, str],
            marketing_channel: str,
            name: str,
            schema_id: str
    ) -> Optional[str]:
        """
        Create a rule in Loki.

        Args:
            rule_attachment_type: The rule attachment type (numeric value)
            labels: Dictionary of labels to apply to the rule
            marketing_channel: The marketing channel for the rule
            name: The name of the rule
            schema_id: The schema ID for the rule

        Returns:
            The ID of the created rule, or None if creation failed
        """
        with self.create_channel() as channel:
            try:
                stub = loki_rule_svc_grpc.RuleServiceStub(channel)
                payload = {
                    "attachment_type": rule_attachment_type,
                    "labels": labels,
                    "marketing_channel": marketing_channel,
                    "name": name,
                    "schema_id": schema_id
                }

                self.logger.info(f"Creating rule: {name}")
                response = stub.CreateRule(
                    rule_svc_pb2.CreateRuleRequest(**payload),
                    timeout=self.timeout_seconds
                )

                rule_id = str(response.id)
                self.logger.info(f"Created rule with ID: {rule_id}")
                return rule_id

            except grpc.RpcError as e:
                self.logger.error(f"gRPC error creating rule: {e.code()}: {e.details()}")
                raise e
            except Exception as e:
                self.logger.error(f"Error creating rule: {e}", exc_info=True)
                raise e

    def create_rule_item(
            self,
            business_case: str,
            info: Dict[str, Any] | str,
            rule_id: str
    ) -> Optional[str]:
        """
        Create a rule item in Loki.

        Args:
            business_case: The business case for the rule item
            info: The rule item information, either as a dictionary or JSON string
            rule_id: The ID of the parent rule

        Returns:
            The ID of the created rule item, or None if creation failed
        """
        with self.create_channel() as channel:
            try:
                stub = loki_rule_svc_grpc.RuleServiceStub(channel)

                # Convert info to JSON string if it's a dictionary
                if isinstance(info, dict):
                    info = json.dumps(info)

                payload = {
                    "business_case": business_case,
                    "info": info,
                    "rule_id": rule_id
                }

                self.logger.info(f"Creating rule item for rule ID: {rule_id}")

                response = stub.CreateRuleItem(
                    rule_svc_pb2.CreateRuleItemRequest(**payload),
                    timeout=self.timeout_seconds
                )

                item_id = str(response.id)
                self.logger.info(f"Created rule item with ID: {item_id}")
                return item_id

            except grpc.RpcError as e:
                self.logger.error(f"gRPC error creating rule item: {e.code()}: {e.details()}")
                return None
            except Exception as e:
                self.logger.error(f"Error creating rule item: {e}", exc_info=True)
                return None

    def find_existing_rule_item_with_details(self, rule_id: str, business_case: str, info: Dict[str, Any] | str) -> Optional[Dict[str, Any]]:
        """
        Find an existing rule item that matches both the business case and info content exactly.

        Args:
            rule_id: The ID of the parent rule
            business_case: The business case to match
            info: The rule item information to match

        Returns:
            Dictionary with rule item details (id, info, version, etc.) or None if not found
        """
        existing_items = self.get_rule_items(rule_id)
        if not existing_items:
            return None

        # Normalize the new info for comparison
        new_info_normalized = self._normalize_info_for_comparison(info)

        for item in existing_items:
            # First, check if a business case matches
            if item['business_case'] == business_case:
                # Then check if info content matches exactly
                existing_info_normalized = self._normalize_info_for_comparison(item['info'])
                if new_info_normalized == existing_info_normalized:
                    return item

        return None

    @staticmethod
    def _normalize_info_for_comparison(info: Dict[str, Any] | str) -> str:
        """
        Normalize info content for comparison by converting to a consistent JSON string format.

        Args:
            info: The info content to normalize

        Returns:
            Normalized JSON string
        """
        if isinstance(info, dict):
            return json.dumps(info, sort_keys=True, separators=(',', ':'))
        elif isinstance(info, str):
            try:
                # Try to parse and re-serialize to normalize formatting
                parsed = json.loads(info)
                return json.dumps(parsed, sort_keys=True, separators=(',', ':'))
            except json.JSONDecodeError:
                # If it's not valid JSON, return as-is
                return info
        else:
            return str(info)

    def create_or_update_rule_item(
            self,
            business_case: str,
            info: Dict[str, Any] | str,
            rule_id: str
    ) -> Tuple[Optional[str], str]:
        """
        Create a new rule item or update an existing one if it already exists and has changes.

        Args:
            business_case: The business case for the rule item
            info: The rule item information, either as a dictionary or JSON string
            rule_id: The ID of the parent rule

        Returns:
            A tuple of (rule_item_id, action) where:
            - rule_item_id: The ID of the created/updated rule item, or None if operation failed
            - action: One of 'created', 'updated', 'no_change', or 'failed'
        """
        # First, try to find an existing rule item with the same business case
        existing_item = self.find_existing_rule_item_with_details(rule_id, business_case, info)

        if existing_item:
            # Found exact match (same business case and same info content)
            # Since we found an exact match, no update is needed
            self.logger.info(f"Found exact match for rule item {existing_item['id']}, no update needed")
            return existing_item['id'], 'no_change'
        else:
            # No exact match found, create a new rule item
            self.logger.info("No existing rule item with matching business case and info found, creating new one...")
            new_item_id = self.create_rule_item(business_case, info, rule_id)
            if new_item_id:
                return new_item_id, 'created'
            else:
                return None, 'failed'

    def get_rule_schemas(self) -> Optional[list]:
        """
        Fetch rule schemas from Loki.

        Returns:
            List of rule schemas or None if there was an error
        """
        with self.create_channel() as channel:
            try:
                stub = loki_rule_svc_grpc.RuleServiceStub(channel)

                offset = 0
                limit = 100  # Default pagination limit
                all_schemas = []
                request_count = 0

                while True:
                    self.logger.info(f"Fetching rule schemas with offset: {offset}, limit: {limit}")

                    # Start timing the request
                    start_time = time.time()

                    # Make the gRPC call with pagination parameters
                    rule_schemas = stub.GetRuleSchemas(
                        rule_svc_pb2.GetRuleSchemasRequest(
                            offset=offset,
                            limit=limit
                        ),
                        timeout=self.timeout_seconds
                    )

                    # Calculate and log latency
                    latency = time.time() - start_time
                    request_count += 1
                    self.logger.info(f"gRPC call latency: {latency:.3f} seconds")

                    # Process schemas in this batch
                    batch_schemas = []
                    for schema in rule_schemas.schemas:
                        batch_schemas.append({
                            'id': schema.id,
                            'name': schema.name,
                            'description': schema.description
                        })

                    # Add this batch to our collection
                    all_schemas.extend(batch_schemas)

                    # Log the progress
                    self.logger.info(f"Fetched {len(batch_schemas)} rule schemas in this batch")

                    # If we received fewer items than the limit, we've reached the end
                    if len(batch_schemas) < limit:
                        break

                    # Move to the next page
                    offset += limit

                self.logger.info(f"Successfully fetched all {len(all_schemas)} rule schemas")
                return all_schemas

            except grpc.RpcError as e:
                self.logger.error(f"gRPC error fetching schemas: {e.code()}: {e.details()}")
                return None
            except Exception as e:
                self.logger.error(f"Error fetching schemas: {e}", exc_info=True)
                return None

    def get_or_create_rule(
            self,
            rule_attachment_type: str,
            labels: Dict[str, str],
            marketing_channel: str,
            name: str,
            schema_id: str
    ):
        with self.create_channel() as channel:
            try:
                stub = loki_rule_svc_grpc.RuleServiceStub(channel)
                offset = 0
                limit = 100

                self.logger.info(f"Searching for rule with name: {name}")

                while True:
                    # Create a request with pagination parameters
                    rule_request = rule_svc_pb2.GetRulesRequest(
                        marketing_channel=marketing_channel,
                        attachment_type=rule_attachment_type,
                        offset=offset,
                        limit=limit
                    )

                    # Make the gRPC call
                    self.logger.info(f"Fetching rules with offset: {offset}, limit: {limit}")
                    rules_response = stub.GetRules(rule_request, timeout=self.timeout_seconds)

                    # Check if the rule exists in this batch
                    for rule in rules_response.rules:
                        if rule.name == name:
                            self.logger.info(f"Rule found: {rule.name} with ID {rule.id}")
                            return rule.id

                    # If we received exactly the limit number of items, we need to check the next page.
                    # If we received fewer than the limit, we've checked all rules
                    if len(rules_response.rules) < limit:
                        break

                    # Move to the next page
                    offset += limit

                # If we've gone through all rules and didn't find a match, create a new rule
                self.logger.info(f"Rule '{name}' not found, creating a new one")
                return self.create_rule(rule_attachment_type, labels, marketing_channel, name, schema_id)

            except grpc.RpcError as e:
                self.logger.error(f"gRPC error fetching rule: {e.code()}: {e.details()}")
                raise e
            except Exception as e:
                self.logger.error(f"Error fetching rule: {e}", exc_info=True)
                raise e
