import json
import logging

import pandas as pd

from models.target import T
from services.loki_service import LokiService
from services.deepthought_service import DeepThoughtService
from config.settings import Settings
from utils.data_transformers import flatten_basebid_operations, flatten_multiplier_operations

logger = logging.getLogger(__name__)


def _generate_df_of_rule_items_to_be_created(
        df: pd.DataFrame,
        target: T,
        rule_type: str,
        is_global: bool,
        initial_rule_priority: int = 0,
        exclude_factors_of_one: bool = True
) -> pd.DataFrame:
    if target.partner == 'TRIVAGO':
        rule_name = f'{target.partner} | {target.brand} | {target.lob} | {target.posa} | {target.auction_type}'
    else:
        rule_name = ' | '.join(target.parts)

    if not exclude_factors_of_one:
        rule_name += " | FACTOR-1"
    rule_priority = initial_rule_priority

    if rule_type not in ['base_bid', 'multiplier']:
        raise ValueError("rule_type must be either 'base_bid' or 'multiplier'")

    # Initialize an empty dataframe with the required columns
    dtypes = {
        'rule_name': str,
        'priority': int,
        'resource': str,
        'is_global': str,
        'operator': str,
        'factor': float,
        'minimum': int,
        'maximum': int,
        'dimensions': object
    }
    rule_items_df = pd.DataFrame(columns=list(dtypes.keys())).astype(dtypes)


    for index, row in df.iterrows():
        min_value = str(row.get('minimum', "")).replace("nan", "")
        max_value = str(row.get('maximum', "")).replace("nan", "")
        if min_value != "" and max_value != "":
            min_max_value = f"{min_value},{max_value}"
        else:
            min_max_value = ""

        try:
            min_value = int(min_value) if min_value else 0
        except ValueError:
            logger.error(f"Invalid min value for row {index}: min_value={min_value}")
            min_value = 0
        try:
            max_value = int(max_value) if max_value else 0
        except ValueError:
            logger.error(f"Invalid max value for row {index}: max_value={max_value}")
            max_value = 0

        operation_type = row.get('operation_type', "").lower()
        if operation_type not in ['minmax', 'factor']:
            raise ValueError("operation_type must be either 'min-max' or 'factor'")

        # set proper operation type and value
        operation_value = ""
        if operation_type == 'minmax':
            operation_type = 'min-max'
        elif operation_type == 'factor':
            try:
                operation_value = float(row.get('factor', 0.0))
            except:
                operation_value = 0.0

        feature_matcher_count = row.get('feature_matcher_count', 0)
        try:
            feature_matchers = json.loads(row.get('feature_matchers_json', []))
        except Exception as e:
            print(f"Error loading feature_matchers: {e}")
            feature_matchers = []

        dimension = ""
        dimension_value = ""
        dimensions = list()
        if feature_matcher_count > 0:
            for feature_matcher in feature_matchers:
                dimension = str(feature_matcher.get('featureName', ""))
                if dimension == "multiplier_value":
                    dimension = "multiplier_level"
                if dimension == 'half_star_rating_score':
                    dimension_value = f"{float(feature_matcher.get('value', 0)):.1f}"
                else:
                    dimension_value = str(feature_matcher.get('value', ""))
                dimensions.append({
                    'dimension': dimension,
                    'dimension_value': dimension_value
                })

        multiplier = str(row.get('multiplier_type', "")).lower()

        if operation_type == 'min-max' and not min_max_value:
            logger.info(f"Skipping rule_type {rule_type} {multiplier} {operation_type} {dimension} {dimension_value} because it has no value.")
            continue

        if operation_type == 'factor':
            if exclude_factors_of_one and operation_value == 1:
                continue
            if operation_value == 0:
                logger.info(f"Skipping rule_type {rule_type} {operation_type} because it has no value.")
                continue


        # Create a dictionary for the rule item and append it to the dataframe
        rule_item = {
            'rule_name': rule_name,
            'priority': rule_priority,
            'resource': multiplier if rule_type == 'multiplier' else rule_type,
            'is_global': str(is_global).lower(),
            'operator': operation_type,
            'factor': 0.0 if operation_type == 'min-max' else operation_value,
            'minimum': min_value if operation_type == 'min-max' else 0,
            'maximum': max_value if operation_type == 'min-max' else 0,
            'dimensions': dimensions
        }
        rule_items_df = pd.concat([rule_items_df, pd.DataFrame([rule_item])], ignore_index=True).astype(dtypes)
        rule_priority += 1

    return rule_items_df


class RuleGenerator:
    """Service for generating and creating rules in Loki based on DeepThought data"""

    def __init__(self, loki_service: LokiService, deepthought_service: DeepThoughtService, settings: Settings):
        """
        Initialize the rule generator.

        Args:
            loki_service: Service for Loki API interactions
            deepthought_service: Service for DeepThought API interactions
            settings: Application settings
        """
        self.loki_service = loki_service
        self.deepthought_service = deepthought_service
        self.settings = settings

    def generate_rule_items(self, target, fbls, global_fbls, exclude_factors_of_one=True) -> pd.DataFrame:
        try:
            basebid_df = flatten_basebid_operations(basebid_operations=fbls.get('basebidOperations'))
        except Exception as e:
            print(f"Error flattening basebid operations: {e}")
            raise e
        multiplier_df = flatten_multiplier_operations(fbls.get('multiplierOperations'))
        global_basebid_df = flatten_basebid_operations(global_fbls.get('basebidOperations'))
        global_multiplier_df = flatten_multiplier_operations(global_fbls.get('multiplierOperations'))

        base_bid_match_cols = [
            'resource',
            'operator',
            'factor',
            'minimum',
            'maximum',
            'dimensions_str'
        ]

        # create rule items for base bids
        base_bid_items = _generate_df_of_rule_items_to_be_created(
            basebid_df,
            target,
            rule_type='base_bid',
            is_global=False,
            exclude_factors_of_one=self.settings.exclude_factors_of_one
        )

        # Create rule items for global base bids
        global_base_bid_items = _generate_df_of_rule_items_to_be_created(
            global_basebid_df,
            target,
            rule_type='base_bid',
            is_global=True,
            exclude_factors_of_one=self.settings.exclude_factors_of_one
        )

        # Convert dimensions to string for comparison
        for df in [base_bid_items, global_base_bid_items]:
            df['dimensions_str'] = df['dimensions'].apply(lambda x: json.dumps(x, sort_keys=True))

        # Remove duplicate global base bid rules
        filtered_base_bid_items = base_bid_items.merge(
            global_base_bid_items[base_bid_match_cols],
            on=base_bid_match_cols,
            how='left',
            indicator=True
        )
        filtered_base_bid_items = filtered_base_bid_items[filtered_base_bid_items['_merge'] == 'left_only'].drop(
            ['_merge', 'dimensions_str'], axis=1)
        global_base_bid_items = global_base_bid_items.drop('dimensions_str', axis=1)

        # Create rule items for multipliers
        multiplier_match_cols = [
            'resource',
            'operator',
            'factor',
            'minimum',
            'maximum',
            'dimensions_str'
        ]

        multiplier_items = _generate_df_of_rule_items_to_be_created(
            multiplier_df,
            target,
            rule_type='multiplier',
            is_global=False,
            exclude_factors_of_one=self.settings.exclude_factors_of_one
        )

        global_multiplier_items = _generate_df_of_rule_items_to_be_created(
            global_multiplier_df,
            target,
            rule_type='multiplier',
            is_global=True,
            exclude_factors_of_one=self.settings.exclude_factors_of_one
        )

        # Convert dimensions to string for comparison
        for df in [multiplier_items, global_multiplier_items]:
            df['dimensions_str'] = df['dimensions'].apply(lambda x: json.dumps(x, sort_keys=True))

        # Remove duplicate multiplier rules
        filtered_multiplier_items = multiplier_items.merge(
            global_multiplier_items[multiplier_match_cols],
            on=multiplier_match_cols,
            how='left',
            indicator=True
        )
        filtered_multiplier_items = filtered_multiplier_items[filtered_multiplier_items['_merge'] == 'left_only'].drop(
            ['_merge', 'dimensions_str'], axis=1)
        global_multiplier_items = global_multiplier_items.drop('dimensions_str', axis=1)

        all_rule_items = pd.concat([
            filtered_base_bid_items,
            global_base_bid_items,
            filtered_multiplier_items,
            global_multiplier_items,
        ], ignore_index=True)

        # Reset priorities to ensure they are sequential
        all_rule_items['priority'] = range(1, len(all_rule_items) + 1)
        all_rule_items['priority'] = all_rule_items['priority']

        return all_rule_items