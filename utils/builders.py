from models.rule import RuleI<PERSON>


def build_rule_item_payload(item: RuleItem, business_case: str, rule_id: str) -> dict:
    """
    Build the payload for a rule item based on its operator type.

    Args:
        item: RuleItem instance containing rule information
        business_case: Business case string
        rule_id: ID of the parent rule

    Returns:
        Dictionary containing the properly structured payload
    """
    # Base info that's common to all operators
    base_info = {
        'priority': item.priority,
        'resource': item.resource,
        'operator': item.operator,
        'dimensions': item.dimensions
    }

    # Add operator-specific fields
    if item.operator == 'factor':
        base_info['factor'] = item.factor
    elif item.operator == 'min-max':
        base_info['minimum'] = item.minimum
        base_info['maximum'] = item.maximum

    return {
        'business_case': business_case,
        'info': base_info,
        'rule_id': rule_id
    }