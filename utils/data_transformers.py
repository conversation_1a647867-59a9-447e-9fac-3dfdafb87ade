import json

import pandas as pd
from typing import List, Dict, Any, Optional


def flatten_basebid_operations(basebid_operations: Dict[str, Any]) -> pd.DataFrame:
    """
    Flatten basebid operations JSON into a pandas DataFrame with rows and columns.

    Args:
        basebid_operations: Dictionary containing basebidOperationList

    Returns:
        DataFrame with flattened structure
    """
    rows = []

    # Get the list of operations
    operation_list = basebid_operations.get('basebidOperationList', [])

    # Iterate through each operation
    for operation in operation_list:
        operation_name = operation.get('operationName', '')
        operation_note = operation.get('operationNote', '')
        operation_order = operation.get('operationOrder', '')
        operation_type = operation.get('operationType', '')

        # Get the rules for this operation and sort them in reverse order
        rules = sorted(operation.get('rules', []), key=lambda x: x.get('ruleOrder', 0), reverse=True)


        # If there are no rules, add a row for just the operation
        if not rules:
            rows.append({
                'operation_name': operation_name,
                'operation_note': operation_note,
                'operation_order': operation_order,
                'operation_type': operation_type,
                'rule_order': "",
                'rule_note': "",
                'factor': "",
                'minimum': "",
                'maximum': "",
                'feature_matchers_json': json.dumps([]),
                'feature_matcher_count': 0
            })

        # Otherwise, add a row for each rule
        for rule in rules:
            rule_order = rule.get('ruleOrder', '')
            rule_note = rule.get('ruleNote', '')
            feature_matchers = rule.get('featureMatcher', [])
            feature_matchers_json = json.dumps(feature_matchers)
            operands = rule.get('operands', {})

            # Extract common operands
            factor = operands.get('factor', "")
            minimum = operands.get('minimum', "")
            maximum = operands.get('maximum', "")

            rows.append({
                'operation_name': operation_name,
                'operation_note': operation_note,
                'operation_order': operation_order,
                'operation_type': operation_type,
                'rule_order': rule_order,
                'rule_note': rule_note,
                'factor': factor,
                'minimum': minimum,
                'maximum': maximum,
                'feature_matchers_json': feature_matchers_json,
                'feature_matcher_count': len(feature_matchers)
            })

    # Convert to DataFrame
    return pd.DataFrame(rows)


def flatten_multiplier_operations(multiplier_operations: Dict[str, Any]) -> pd.DataFrame:
    """
    Flatten multiplier operations JSON into a pandas DataFrame with rows and columns.

    Args:
        multiplier_operations: Dictionary containing multiplierOperationList

    Returns:
        DataFrame with flattened structure
    """
    rows = []

    # Get creation date and operation list
    created = multiplier_operations.get('created', '')
    operation_list = multiplier_operations.get('multiplierOperationList', [])

    # Iterate through each operation
    for operation in operation_list:
        operation_name = operation.get('operationName', '')
        operation_note = operation.get('operationNote', '')
        operation_order = operation.get('operationOrder', '')
        operation_type = operation.get('operationType', '')
        multiplier_type = operation.get('multiplierType', '')

        # Get the rules for this operation
        rules = sorted(operation.get('rules', []), key=lambda x: x.get('ruleOrder', 0), reverse=True)

        # If there are no rules, add a row for just the operation
        if not rules:
            rows.append({
                'created': created,
                'operation_name': operation_name,
                'operation_note': operation_note,
                'operation_order': operation_order,
                'operation_type': operation_type,
                'multiplier_type': multiplier_type,
                'rule_order': "",
                'rule_note': "",
                'factor': "",
                'feature_matchers_json': json.dumps([]),
                'feature_matcher_count': 0,
            })

        # Otherwise, add a row for each rule
        for rule in rules:
            rule_order = rule.get('ruleOrder', '')
            rule_note = rule.get('ruleNote', '')
            feature_matchers = rule.get('featureMatcher', [])
            feature_matchers_json = json.dumps(feature_matchers)
            operands = rule.get('operands', {})


            # Extract common operands
            factor = operands.get('factor', "")
            minimum = operands.get('minimum', "")
            maximum = operands.get('maximum', "")

            rows.append({
                'operation_name': operation_name,
                'operation_note': operation_note,
                'operation_order': operation_order,
                'operation_type': operation_type,
                'rule_order': rule_order,
                'rule_note': rule_note,
                'multiplier_type': multiplier_type,
                'factor': factor,
                'minimum': minimum,
                'maximum': maximum,
                'feature_matchers_json': feature_matchers_json,
                'feature_matcher_count': len(feature_matchers)
            })

    # Convert to DataFrame
    df = pd.DataFrame(rows)
    return df
