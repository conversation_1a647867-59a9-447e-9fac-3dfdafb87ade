import logging
import os
import time
from typing import <PERSON><PERSON>


def setup_target_logger(target_string: str) -> <PERSON><PERSON>[logging.Logger, logging.FileHandler]:
    """
    Set up a logger for a specific target with a dedicated file handler.

    Args:
        target_string: The target string identifier

    Returns:
        Tuple containing (logger, file_handler)
    """
    # Create a safe filename for the log file
    safe_target_name = target_string.replace('|', '_').replace(':', '_')
    timestamp = time.strftime("%Y%m%d-%H%M%S")

    # Create a logs directory if it doesn't exist
    os.makedirs("logs", exist_ok=True)
    os.makedirs(f"logs/{safe_target_name}", exist_ok=True)

    log_filename = f"logs/{safe_target_name}/{timestamp}.log"

    # Create a target-specific logger
    target_logger = logging.getLogger(f"RuleMigrator.{safe_target_name}")
    target_logger.setLevel(logging.INFO)

    # Check if this logger already has handlers to avoid duplicates
    if not target_logger.handlers:
        # Create a file handler for this target
        file_handler = logging.FileHandler(log_filename)
        file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(file_formatter)

        # Add the handler to the logger
        target_logger.addHandler(file_handler)

        return target_logger, file_handler
    else:
        # Return existing handler
        return target_logger, target_logger.handlers[0]
